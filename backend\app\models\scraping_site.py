"""
Scraping site model for managing video sources.
"""

from sqlalchemy import Column, Integer, String, Text, Boolean, DateTime, JSON, Float
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
from app.models.database import Base


class ScrapingSite(Base):
    """Model for managing video scraping sites."""
    
    __tablename__ = "scraping_sites"
    
    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(255), nullable=False, index=True)
    base_url = Column(String(500), nullable=False, unique=True)
    description = Column(Text)
    
    # Site configuration
    is_active = Column(Boolean, default=True, index=True)
    priority = Column(Integer, default=1, index=True)  # Higher priority = scraped first
    scraping_interval_hours = Column(Integer, default=24)  # How often to scrape
    max_pages_per_scrape = Column(Integer, default=10)
    
    # Site metadata
    site_type = Column(String(50), default="video")  # video, image, etc.
    content_rating = Column(String(20), default="adult")  # adult, general, mixed
    language = Column(String(10), default="en")
    country = Column(String(10))
    
    # Scraping configuration
    scraping_config = Column(JSON)  # Store scraping rules as JSON
    headers = Column(JSON)  # Custom headers for requests
    rate_limit_delay = Column(Float, default=1.0)  # Seconds between requests
    
    # Authentication (if needed)
    requires_auth = Column(Boolean, default=False)
    auth_config = Column(JSON)  # Store auth details (encrypted)
    
    # Status tracking
    last_scraped_at = Column(DateTime)
    last_successful_scrape = Column(DateTime)
    last_error = Column(Text)
    total_videos_scraped = Column(Integer, default=0)
    failed_scrape_count = Column(Integer, default=0)
    
    # Quality metrics
    average_video_quality = Column(Float)
    success_rate = Column(Float, default=0.0)  # Percentage of successful scrapes
    
    # Timestamps
    created_at = Column(DateTime, server_default=func.now())
    updated_at = Column(DateTime, server_default=func.now(), onupdate=func.now())
    created_by = Column(String(255))  # Admin who added this site
    
    def __repr__(self):
        return f"<ScrapingSite(id={self.id}, name='{self.name}', url='{self.base_url}')>"
    
    @property
    def status(self):
        """Get current status of the scraping site."""
        if not self.is_active:
            return "inactive"
        elif self.failed_scrape_count > 5:
            return "failing"
        elif self.last_successful_scrape is None:
            return "never_scraped"
        else:
            return "active"
    
    def to_dict(self):
        """Convert to dictionary for API responses."""
        return {
            "id": self.id,
            "name": self.name,
            "base_url": self.base_url,
            "description": self.description,
            "is_active": self.is_active,
            "priority": self.priority,
            "scraping_interval_hours": self.scraping_interval_hours,
            "max_pages_per_scrape": self.max_pages_per_scrape,
            "site_type": self.site_type,
            "content_rating": self.content_rating,
            "language": self.language,
            "country": self.country,
            "rate_limit_delay": self.rate_limit_delay,
            "requires_auth": self.requires_auth,
            "last_scraped_at": self.last_scraped_at.isoformat() if self.last_scraped_at else None,
            "last_successful_scrape": self.last_successful_scrape.isoformat() if self.last_successful_scrape else None,
            "last_error": self.last_error,
            "total_videos_scraped": self.total_videos_scraped,
            "failed_scrape_count": self.failed_scrape_count,
            "average_video_quality": self.average_video_quality,
            "success_rate": self.success_rate,
            "status": self.status,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None,
            "created_by": self.created_by,
        }


class ScrapeJob(Base):
    """Model for tracking individual scraping jobs."""
    
    __tablename__ = "scrape_jobs"
    
    id = Column(Integer, primary_key=True, index=True)
    site_id = Column(Integer, nullable=False, index=True)
    
    # Job details
    status = Column(String(20), default="pending", index=True)  # pending, running, completed, failed
    started_at = Column(DateTime)
    completed_at = Column(DateTime)
    
    # Results
    pages_scraped = Column(Integer, default=0)
    videos_found = Column(Integer, default=0)
    videos_added = Column(Integer, default=0)
    videos_updated = Column(Integer, default=0)
    videos_skipped = Column(Integer, default=0)
    
    # Error tracking
    error_message = Column(Text)
    error_count = Column(Integer, default=0)
    
    # Metadata
    scrape_config_snapshot = Column(JSON)  # Config used for this job
    log_data = Column(JSON)  # Detailed logs
    
    # Timestamps
    created_at = Column(DateTime, server_default=func.now())
    
    def __repr__(self):
        return f"<ScrapeJob(id={self.id}, site_id={self.site_id}, status='{self.status}')>"
    
    @property
    def duration_seconds(self):
        """Calculate job duration in seconds."""
        if self.started_at and self.completed_at:
            return (self.completed_at - self.started_at).total_seconds()
        return None
    
    def to_dict(self):
        """Convert to dictionary for API responses."""
        return {
            "id": self.id,
            "site_id": self.site_id,
            "status": self.status,
            "started_at": self.started_at.isoformat() if self.started_at else None,
            "completed_at": self.completed_at.isoformat() if self.completed_at else None,
            "duration_seconds": self.duration_seconds,
            "pages_scraped": self.pages_scraped,
            "videos_found": self.videos_found,
            "videos_added": self.videos_added,
            "videos_updated": self.videos_updated,
            "videos_skipped": self.videos_skipped,
            "error_message": self.error_message,
            "error_count": self.error_count,
            "created_at": self.created_at.isoformat() if self.created_at else None,
        }


class AdminUser(Base):
    """Model for admin users who can manage scraping sites."""
    
    __tablename__ = "admin_users"
    
    id = Column(Integer, primary_key=True, index=True)
    username = Column(String(100), unique=True, nullable=False, index=True)
    email = Column(String(255), unique=True, nullable=False, index=True)
    hashed_password = Column(String(255), nullable=False)
    
    # Role and permissions
    role = Column(String(20), default="moderator", index=True)  # owner, admin, moderator
    is_active = Column(Boolean, default=True, index=True)
    is_superuser = Column(Boolean, default=False)
    
    # Profile
    full_name = Column(String(255))
    avatar_url = Column(String(500))
    
    # Activity tracking
    last_login_at = Column(DateTime)
    login_count = Column(Integer, default=0)
    
    # Timestamps
    created_at = Column(DateTime, server_default=func.now())
    updated_at = Column(DateTime, server_default=func.now(), onupdate=func.now())
    
    def __repr__(self):
        return f"<AdminUser(id={self.id}, username='{self.username}', role='{self.role}')>"
    
    @property
    def permissions(self):
        """Get permissions based on role."""
        if self.role == "owner":
            return ["all"]
        elif self.role == "admin":
            return ["manage_sites", "manage_scraping", "view_analytics", "manage_users"]
        elif self.role == "moderator":
            return ["manage_sites", "view_analytics"]
        else:
            return ["view_analytics"]
    
    def has_permission(self, permission: str) -> bool:
        """Check if user has specific permission."""
        if not self.is_active:
            return False
        if "all" in self.permissions:
            return True
        return permission in self.permissions
    
    def to_dict(self, include_sensitive=False):
        """Convert to dictionary for API responses."""
        data = {
            "id": self.id,
            "username": self.username,
            "email": self.email if include_sensitive else None,
            "role": self.role,
            "is_active": self.is_active,
            "is_superuser": self.is_superuser,
            "full_name": self.full_name,
            "avatar_url": self.avatar_url,
            "last_login_at": self.last_login_at.isoformat() if self.last_login_at else None,
            "login_count": self.login_count,
            "permissions": self.permissions,
            "created_at": self.created_at.isoformat() if self.created_at else None,
        }
        return data
