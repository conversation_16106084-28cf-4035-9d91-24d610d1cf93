import React from 'react'
import { <PERSON> } from 'react-router-dom'
import { <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, Calendar } from 'lucide-react'
import { Button } from '@/components/ui/Button'
import { Badge } from '@/components/ui/Badge'
import { useFavorites } from '@/store'
import { formatDuration, formatResolution } from '@/services/api'
import { cn } from '@/utils/cn'
import type { Video } from '@/types'

interface VideoListProps {
  videos: Video[]
  className?: string
}

export function VideoList({ videos, className }: VideoListProps) {
  const { isFavorite, toggleFavorite } = useFavorites()

  if (videos.length === 0) {
    return (
      <div className="text-center py-12">
        <p className="text-muted-foreground">No videos found</p>
      </div>
    )
  }

  const handleFavoriteClick = (e: React.MouseEvent, videoId: number) => {
    e.preventDefault()
    e.stopPropagation()
    toggleFavorite(videoId)
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    })
  }

  return (
    <div className={cn("space-y-4", className)}>
      {videos.map((video) => (
        <div
          key={video.id}
          className="group bg-card rounded-lg border hover:shadow-md transition-all duration-200 overflow-hidden"
        >
          <Link to={`/video/${video.id}`} className="block">
            <div className="flex gap-4 p-4">
              {/* Thumbnail */}
              <div className="relative flex-shrink-0 w-48 h-28 bg-muted rounded-lg overflow-hidden">
                {video.thumbnail_url ? (
                  <img
                    src={video.thumbnail_url}
                    alt={video.title}
                    className="w-full h-full object-cover transition-transform duration-200 group-hover:scale-105"
                    loading="lazy"
                  />
                ) : (
                  <div className="w-full h-full flex items-center justify-center bg-muted">
                    <Play className="h-8 w-8 text-muted-foreground" />
                  </div>
                )}
                
                {/* Overlay */}
                <div className="absolute inset-0 bg-black/0 group-hover:bg-black/20 transition-colors duration-200" />
                
                {/* Play button */}
                <div className="absolute inset-0 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-200">
                  <div className="bg-primary/90 rounded-full p-2">
                    <Play className="h-4 w-4 text-primary-foreground fill-current" />
                  </div>
                </div>
                
                {/* Duration */}
                <div className="absolute bottom-2 right-2 bg-black/80 text-white text-xs px-2 py-1 rounded">
                  {formatDuration(video.duration)}
                </div>
                
                {/* Quality badge */}
                {(video.is_4k || video.is_hd) && (
                  <div className="absolute top-2 left-2 bg-primary text-primary-foreground text-xs px-2 py-1 rounded font-medium">
                    {video.is_4k ? '4K' : 'HD'}
                  </div>
                )}
              </div>

              {/* Content */}
              <div className="flex-1 min-w-0 space-y-3">
                {/* Title */}
                <h3 className="font-semibold text-lg line-clamp-2 group-hover:text-primary transition-colors">
                  {video.title}
                </h3>
                
                {/* Description */}
                {video.description && (
                  <p className="text-sm text-muted-foreground line-clamp-2">
                    {video.description}
                  </p>
                )}
                
                {/* Metadata */}
                <div className="flex flex-wrap items-center gap-4 text-sm text-muted-foreground">
                  <div className="flex items-center gap-1">
                    <Eye className="h-3 w-3" />
                    <span>{video.view_count.toLocaleString()} views</span>
                  </div>
                  
                  <div className="flex items-center gap-1">
                    <Star className="h-3 w-3" />
                    <span>{video.rating.toFixed(1)}/10</span>
                  </div>
                  
                  <div className="flex items-center gap-1">
                    <Monitor className="h-3 w-3" />
                    <span>{formatResolution(video.resolution_width, video.resolution_height)}</span>
                  </div>
                  
                  <div className="flex items-center gap-1">
                    <Calendar className="h-3 w-3" />
                    <span>{formatDate(video.date_added)}</span>
                  </div>
                </div>
                
                {/* Categories */}
                {video.categories && video.categories.length > 0 && (
                  <div className="flex flex-wrap gap-1">
                    {video.categories.slice(0, 4).map((category) => (
                      <Link
                        key={category.id}
                        to={`/category/${category.slug}`}
                        className="inline-block"
                        onClick={(e) => e.stopPropagation()}
                      >
                        <Badge variant="secondary" className="text-xs hover:bg-secondary/80 transition-colors">
                          {category.display_name || category.name}
                        </Badge>
                      </Link>
                    ))}
                    {video.categories.length > 4 && (
                      <Badge variant="outline" className="text-xs">
                        +{video.categories.length - 4} more
                      </Badge>
                    )}
                  </div>
                )}

                {/* Performers */}
                {video.performers && video.performers.length > 0 && (
                  <div className="flex flex-wrap gap-1">
                    {video.performers.slice(0, 3).map((performer) => (
                      <Link
                        key={performer.id}
                        to={`/performer/${performer.slug}`}
                        className="inline-block"
                        onClick={(e) => e.stopPropagation()}
                      >
                        <Badge variant="outline" className="text-xs hover:bg-accent transition-colors">
                          {performer.stage_name || performer.name}
                        </Badge>
                      </Link>
                    ))}
                    {video.performers.length > 3 && (
                      <Badge variant="outline" className="text-xs">
                        +{video.performers.length - 3} more
                      </Badge>
                    )}
                  </div>
                )}
              </div>

              {/* Actions */}
              <div className="flex-shrink-0 flex flex-col items-center gap-2">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={(e) => handleFavoriteClick(e, video.id)}
                  className={cn(
                    "opacity-0 group-hover:opacity-100 transition-opacity duration-200",
                    isFavorite(video.id) && "opacity-100 text-red-500 hover:text-red-600"
                  )}
                >
                  <Heart className={cn("h-4 w-4", isFavorite(video.id) && "fill-current")} />
                </Button>

                {/* Quality Score */}
                {video.quality_score && (
                  <div className="text-xs text-center">
                    <div className="text-muted-foreground">Quality</div>
                    <div className="font-medium">{Math.round(video.quality_score)}/100</div>
                  </div>
                )}
              </div>
            </div>
          </Link>
        </div>
      ))}
    </div>
  )
}
