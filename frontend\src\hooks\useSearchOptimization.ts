import { useState, useEffect, useCallback, useMemo } from 'react'
import { useQuery } from '@tanstack/react-query'
import { searchVideos } from '@/services/api'
import { useAppStore, useSearchStore } from '@/store'
import type { FilterState, Video } from '@/types'

interface UseSearchOptimizationProps {
  query: string
  filters: FilterState
  enabled?: boolean
}

interface SearchMetrics {
  searchTime: number
  resultCount: number
  cacheHit: boolean
  queryComplexity: number
}

export function useSearchOptimization({
  query,
  filters,
  enabled = true,
}: UseSearchOptimizationProps) {
  const [searchMetrics, setSearchMetrics] = useState<SearchMetrics | null>(null)
  const [searchStartTime, setSearchStartTime] = useState<number>(0)
  const { addRecentSearch } = useAppStore()

  // Calculate query complexity score
  const queryComplexity = useMemo(() => {
    let complexity = 0
    
    // Base complexity for query length
    complexity += Math.min(query.length / 10, 5)
    
    // Add complexity for filters
    complexity += filters.categories.length * 0.5
    complexity += filters.performers.length * 0.5
    
    if (filters.minDuration || filters.maxDuration) complexity += 1
    if (filters.minRating) complexity += 1
    
    return Math.round(complexity * 10) / 10
  }, [query, filters])

  // Debounced search query
  const debouncedQuery = useDebounce(query, 300)

  // Search query with performance tracking
  const {
    data,
    isLoading,
    error,
    refetch,
    isFetching,
    dataUpdatedAt,
  } = useQuery({
    queryKey: ['search', debouncedQuery, filters],
    queryFn: async () => {
      setSearchStartTime(performance.now())
      
      const result = await searchVideos({
        query: debouncedQuery,
        filters,
        skip: 0,
        limit: 20,
      })
      
      const searchTime = performance.now() - searchStartTime
      
      setSearchMetrics({
        searchTime,
        resultCount: result.total,
        cacheHit: false, // Would be determined by cache headers
        queryComplexity,
      })
      
      return result
    },
    enabled: enabled && !!debouncedQuery.trim(),
    staleTime: 5 * 60 * 1000, // 5 minutes
    cacheTime: 10 * 60 * 1000, // 10 minutes
    retry: 2,
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
  })

  // Track successful searches
  useEffect(() => {
    if (data && debouncedQuery.trim()) {
      addRecentSearch(debouncedQuery)
    }
  }, [data, debouncedQuery, addRecentSearch])

  // Search suggestions based on current query
  const searchSuggestions = useMemo(() => {
    if (!query.trim() || query.length < 2) return []
    
    const suggestions = []
    
    // Add quality-based suggestions
    if (!filters.categories.some(cat => ['hd', '4k'].includes(cat.toLowerCase()))) {
      suggestions.push(`${query} HD`, `${query} 4K`)
    }
    
    // Add duration-based suggestions
    if (!filters.minDuration && !filters.maxDuration) {
      suggestions.push(`${query} short`, `${query} long`)
    }
    
    // Add popular category combinations
    const popularCategories = ['action', 'comedy', 'drama', 'thriller']
    popularCategories.forEach(cat => {
      if (!query.toLowerCase().includes(cat) && !filters.categories.includes(cat)) {
        suggestions.push(`${query} ${cat}`)
      }
    })
    
    return suggestions.slice(0, 5)
  }, [query, filters])

  // Performance recommendations
  const performanceRecommendations = useMemo(() => {
    const recommendations = []
    
    if (searchMetrics) {
      if (searchMetrics.searchTime > 2000) {
        recommendations.push({
          type: 'performance',
          message: 'Search is slow. Try using fewer filters or more specific terms.',
          severity: 'warning'
        })
      }
      
      if (searchMetrics.resultCount === 0) {
        recommendations.push({
          type: 'results',
          message: 'No results found. Try broader search terms or remove some filters.',
          severity: 'info'
        })
      }
      
      if (searchMetrics.resultCount > 10000) {
        recommendations.push({
          type: 'results',
          message: 'Too many results. Add filters to narrow down your search.',
          severity: 'info'
        })
      }
      
      if (queryComplexity > 8) {
        recommendations.push({
          type: 'complexity',
          message: 'Complex search query. Consider simplifying for better performance.',
          severity: 'warning'
        })
      }
    }
    
    return recommendations
  }, [searchMetrics, queryComplexity])

  // Optimized retry function
  const optimizedRetry = useCallback(() => {
    setSearchMetrics(null)
    refetch()
  }, [refetch])

  // Search analytics data
  const analytics = useMemo(() => ({
    query: debouncedQuery,
    totalResults: data?.total || 0,
    searchTime: searchMetrics?.searchTime,
    appliedFilters: Object.values(filters).filter(Boolean).length,
    suggestions: searchSuggestions,
    recommendations: performanceRecommendations,
    queryComplexity,
    isOptimized: searchMetrics?.searchTime ? searchMetrics.searchTime < 1000 : false,
  }), [
    debouncedQuery,
    data?.total,
    searchMetrics?.searchTime,
    filters,
    searchSuggestions,
    performanceRecommendations,
    queryComplexity,
  ])

  return {
    // Search results
    results: data?.videos || [],
    totalResults: data?.total || 0,
    hasMore: data?.has_more || false,
    
    // Loading states
    isLoading: isLoading || isFetching,
    error: error?.message || null,
    
    // Performance data
    searchMetrics,
    analytics,
    
    // Actions
    retry: optimizedRetry,
    
    // Optimization flags
    isOptimized: analytics.isOptimized,
    needsOptimization: performanceRecommendations.length > 0,
  }
}

// Debounce hook for search optimization
function useDebounce<T>(value: T, delay: number): T {
  const [debouncedValue, setDebouncedValue] = useState<T>(value)

  useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedValue(value)
    }, delay)

    return () => {
      clearTimeout(handler)
    }
  }, [value, delay])

  return debouncedValue
}
