import React, { useState } from 'react'
import { Setting<PERSON>, User, <PERSON><PERSON>, Monitor, Volume2, Eye, Clock, Star, Shield } from 'lucide-react'
import { Button } from '@/components/ui/Button'
import { Badge } from '@/components/ui/Badge'
import { Slider } from '@/components/ui/Slider'
import { Checkbox } from '@/components/ui/Checkbox'
import { useAppStore, useTheme } from '@/store'
import { cn } from '@/utils/cn'
import type { UserPreferences } from '@/types'

interface UserPreferencesProps {
  onClose?: () => void
  className?: string
}

export function UserPreferences({ onClose, className }: UserPreferencesProps) {
  const { userPreferences, setUserPreferences } = useAppStore()
  const { theme, setTheme } = useTheme()
  const [activeTab, setActiveTab] = useState<'general' | 'display' | 'playback' | 'privacy'>('general')

  const updatePreference = <K extends keyof UserPreferences>(
    key: K,
    value: UserPreferences[K]
  ) => {
    setUserPreferences({ [key]: value })
  }

  const tabs = [
    { id: 'general', label: 'General', icon: Settings },
    { id: 'display', label: 'Display', icon: Monitor },
    { id: 'playback', label: 'Playback', icon: Volume2 },
    { id: 'privacy', label: 'Privacy', icon: Shield },
  ] as const

  const languages = [
    { code: 'en', name: 'English' },
    { code: 'es', name: 'Español' },
    { code: 'fr', name: 'Français' },
    { code: 'de', name: 'Deutsch' },
    { code: 'it', name: 'Italiano' },
    { code: 'pt', name: 'Português' },
    { code: 'ru', name: 'Русский' },
    { code: 'ja', name: '日本語' },
    { code: 'ko', name: '한국어' },
    { code: 'zh', name: '中文' },
  ]

  const qualityOptions = [
    { value: 'auto', label: 'Auto (Recommended)' },
    { value: '4k', label: '4K Ultra HD' },
    { value: '1080p', label: 'Full HD (1080p)' },
    { value: '720p', label: 'HD (720p)' },
    { value: '480p', label: 'Standard (480p)' },
  ]

  return (
    <div className={cn("bg-card border rounded-lg shadow-lg max-w-4xl mx-auto", className)}>
      {/* Header */}
      <div className="flex items-center justify-between p-6 border-b">
        <div className="flex items-center gap-3">
          <div className="p-2 bg-primary/10 rounded-lg">
            <User className="h-5 w-5 text-primary" />
          </div>
          <div>
            <h2 className="text-xl font-semibold">User Preferences</h2>
            <p className="text-sm text-muted-foreground">
              Customize your viewing experience
            </p>
          </div>
        </div>
        {onClose && (
          <Button variant="ghost" size="sm" onClick={onClose}>
            ×
          </Button>
        )}
      </div>

      <div className="flex">
        {/* Sidebar */}
        <div className="w-48 border-r p-4 space-y-1">
          {tabs.map((tab) => {
            const Icon = tab.icon
            return (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={cn(
                  "w-full flex items-center gap-3 px-3 py-2 rounded-lg text-left transition-colors",
                  activeTab === tab.id
                    ? "bg-primary text-primary-foreground"
                    : "hover:bg-muted"
                )}
              >
                <Icon className="h-4 w-4" />
                <span className="text-sm font-medium">{tab.label}</span>
              </button>
            )
          })}
        </div>

        {/* Content */}
        <div className="flex-1 p-6">
          {activeTab === 'general' && (
            <div className="space-y-6">
              <div>
                <h3 className="text-lg font-semibold mb-4">General Settings</h3>
                
                {/* Language */}
                <div className="space-y-3">
                  <label className="text-sm font-medium">Language</label>
                  <select
                    value={userPreferences.language}
                    onChange={(e) => updatePreference('language', e.target.value)}
                    className="w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-ring"
                  >
                    {languages.map((lang) => (
                      <option key={lang.code} value={lang.code}>
                        {lang.name}
                      </option>
                    ))}
                  </select>
                </div>

                {/* Items per page */}
                <div className="space-y-3">
                  <label className="text-sm font-medium">
                    Items per page: {userPreferences.items_per_page}
                  </label>
                  <Slider
                    value={[userPreferences.items_per_page]}
                    onValueChange={([value]) => updatePreference('items_per_page', value)}
                    min={10}
                    max={100}
                    step={10}
                    className="w-full"
                  />
                  <div className="flex justify-between text-xs text-muted-foreground">
                    <span>10</span>
                    <span>50</span>
                    <span>100</span>
                  </div>
                </div>

                {/* Favorite Categories */}
                <div className="space-y-3">
                  <label className="text-sm font-medium">Favorite Categories</label>
                  <p className="text-xs text-muted-foreground">
                    Select your preferred content categories for personalized recommendations
                  </p>
                  <div className="flex flex-wrap gap-2">
                    {['Action', 'Comedy', 'Drama', 'Thriller', 'Sci-Fi', 'Romance', 'Horror', 'Documentary'].map((category) => (
                      <button
                        key={category}
                        onClick={() => {
                          const current = userPreferences.favorite_categories || []
                          const updated = current.includes(category)
                            ? current.filter(c => c !== category)
                            : [...current, category]
                          updatePreference('favorite_categories', updated)
                        }}
                        className={cn(
                          "px-3 py-1 rounded-full text-xs border transition-colors",
                          (userPreferences.favorite_categories || []).includes(category)
                            ? "bg-primary text-primary-foreground border-primary"
                            : "hover:bg-muted"
                        )}
                      >
                        {category}
                      </button>
                    ))}
                  </div>
                </div>
              </div>
            </div>
          )}

          {activeTab === 'display' && (
            <div className="space-y-6">
              <div>
                <h3 className="text-lg font-semibold mb-4">Display Settings</h3>
                
                {/* Theme */}
                <div className="space-y-3">
                  <label className="text-sm font-medium flex items-center gap-2">
                    <Palette className="h-4 w-4" />
                    Theme
                  </label>
                  <div className="flex gap-2">
                    {[
                      { value: 'light', label: 'Light', icon: '☀️' },
                      { value: 'dark', label: 'Dark', icon: '🌙' },
                      { value: 'system', label: 'System', icon: '💻' },
                    ].map((option) => (
                      <button
                        key={option.value}
                        onClick={() => setTheme(option.value as any)}
                        className={cn(
                          "flex items-center gap-2 px-4 py-2 rounded-lg border transition-colors",
                          theme === option.value
                            ? "bg-primary text-primary-foreground border-primary"
                            : "hover:bg-muted"
                        )}
                      >
                        <span>{option.icon}</span>
                        <span className="text-sm">{option.label}</span>
                      </button>
                    ))}
                  </div>
                </div>

                {/* Grid Layout */}
                <div className="space-y-3">
                  <label className="text-sm font-medium">Grid Layout</label>
                  <div className="flex gap-2">
                    {[
                      { value: 'compact', label: 'Compact', cols: '6 per row' },
                      { value: 'comfortable', label: 'Comfortable', cols: '4 per row' },
                      { value: 'spacious', label: 'Spacious', cols: '3 per row' },
                    ].map((option) => (
                      <button
                        key={option.value}
                        onClick={() => updatePreference('grid_layout' as any, option.value)}
                        className={cn(
                          "flex flex-col items-center gap-1 px-4 py-3 rounded-lg border transition-colors",
                          (userPreferences as any).grid_layout === option.value
                            ? "bg-primary text-primary-foreground border-primary"
                            : "hover:bg-muted"
                        )}
                      >
                        <span className="text-sm font-medium">{option.label}</span>
                        <span className="text-xs text-muted-foreground">{option.cols}</span>
                      </button>
                    ))}
                  </div>
                </div>

                {/* Show Thumbnails */}
                <div className="flex items-center justify-between">
                  <div>
                    <label className="text-sm font-medium flex items-center gap-2">
                      <Eye className="h-4 w-4" />
                      Show Video Thumbnails
                    </label>
                    <p className="text-xs text-muted-foreground">
                      Display preview images for videos
                    </p>
                  </div>
                  <Checkbox
                    checked={(userPreferences as any).show_thumbnails !== false}
                    onCheckedChange={(checked) => updatePreference('show_thumbnails' as any, checked)}
                  />
                </div>
              </div>
            </div>
          )}

          {activeTab === 'playback' && (
            <div className="space-y-6">
              <div>
                <h3 className="text-lg font-semibold mb-4">Playback Settings</h3>
                
                {/* Auto Play */}
                <div className="flex items-center justify-between">
                  <div>
                    <label className="text-sm font-medium">Auto Play</label>
                    <p className="text-xs text-muted-foreground">
                      Automatically start playing videos when opened
                    </p>
                  </div>
                  <Checkbox
                    checked={userPreferences.auto_play}
                    onCheckedChange={(checked) => updatePreference('auto_play', checked)}
                  />
                </div>

                {/* Quality Preference */}
                <div className="space-y-3">
                  <label className="text-sm font-medium">Default Quality</label>
                  <select
                    value={userPreferences.quality_preference}
                    onChange={(e) => updatePreference('quality_preference', e.target.value as any)}
                    className="w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-ring"
                  >
                    {qualityOptions.map((option) => (
                      <option key={option.value} value={option.value}>
                        {option.label}
                      </option>
                    ))}
                  </select>
                </div>

                {/* Volume */}
                <div className="space-y-3">
                  <label className="text-sm font-medium flex items-center gap-2">
                    <Volume2 className="h-4 w-4" />
                    Default Volume: {((userPreferences as any).default_volume || 50)}%
                  </label>
                  <Slider
                    value={[(userPreferences as any).default_volume || 50]}
                    onValueChange={([value]) => updatePreference('default_volume' as any, value)}
                    min={0}
                    max={100}
                    step={5}
                    className="w-full"
                  />
                </div>

                {/* Playback Speed */}
                <div className="space-y-3">
                  <label className="text-sm font-medium">Default Playback Speed</label>
                  <div className="flex gap-2">
                    {[0.5, 0.75, 1, 1.25, 1.5, 2].map((speed) => (
                      <button
                        key={speed}
                        onClick={() => updatePreference('playback_speed' as any, speed)}
                        className={cn(
                          "px-3 py-1 rounded border text-sm transition-colors",
                          (userPreferences as any).playback_speed === speed
                            ? "bg-primary text-primary-foreground border-primary"
                            : "hover:bg-muted"
                        )}
                      >
                        {speed}x
                      </button>
                    ))}
                  </div>
                </div>
              </div>
            </div>
          )}

          {activeTab === 'privacy' && (
            <div className="space-y-6">
              <div>
                <h3 className="text-lg font-semibold mb-4">Privacy Settings</h3>
                
                {/* View History */}
                <div className="flex items-center justify-between">
                  <div>
                    <label className="text-sm font-medium flex items-center gap-2">
                      <Clock className="h-4 w-4" />
                      Save View History
                    </label>
                    <p className="text-xs text-muted-foreground">
                      Keep track of videos you've watched for recommendations
                    </p>
                  </div>
                  <Checkbox
                    checked={(userPreferences as any).save_history !== false}
                    onCheckedChange={(checked) => updatePreference('save_history' as any, checked)}
                  />
                </div>

                {/* Analytics */}
                <div className="flex items-center justify-between">
                  <div>
                    <label className="text-sm font-medium">Usage Analytics</label>
                    <p className="text-xs text-muted-foreground">
                      Help improve the platform by sharing anonymous usage data
                    </p>
                  </div>
                  <Checkbox
                    checked={(userPreferences as any).analytics_enabled !== false}
                    onCheckedChange={(checked) => updatePreference('analytics_enabled' as any, checked)}
                  />
                </div>

                {/* Personalized Recommendations */}
                <div className="flex items-center justify-between">
                  <div>
                    <label className="text-sm font-medium flex items-center gap-2">
                      <Star className="h-4 w-4" />
                      Personalized Recommendations
                    </label>
                    <p className="text-xs text-muted-foreground">
                      Show content recommendations based on your viewing history
                    </p>
                  </div>
                  <Checkbox
                    checked={(userPreferences as any).personalized_recommendations !== false}
                    onCheckedChange={(checked) => updatePreference('personalized_recommendations' as any, checked)}
                  />
                </div>

                {/* Data Export */}
                <div className="pt-4 border-t">
                  <h4 className="text-sm font-medium mb-3">Data Management</h4>
                  <div className="flex gap-2">
                    <Button variant="outline" size="sm">
                      Export My Data
                    </Button>
                    <Button variant="outline" size="sm">
                      Clear History
                    </Button>
                    <Button variant="destructive" size="sm">
                      Delete Account
                    </Button>
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Footer */}
      <div className="flex items-center justify-between p-6 border-t bg-muted/50">
        <p className="text-xs text-muted-foreground">
          Changes are saved automatically
        </p>
        <div className="flex gap-2">
          <Button variant="outline" size="sm">
            Reset to Defaults
          </Button>
          {onClose && (
            <Button size="sm" onClick={onClose}>
              Done
            </Button>
          )}
        </div>
      </div>
    </div>
  )
}
