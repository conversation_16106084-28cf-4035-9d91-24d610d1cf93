@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --background: 0 0% 100%;
  --foreground: 222.2 84% 4.9%;
  --card: 0 0% 100%;
  --card-foreground: 222.2 84% 4.9%;
  --popover: 0 0% 100%;
  --popover-foreground: 222.2 84% 4.9%;
  --primary: 221.2 83.2% 53.3%;
  --primary-foreground: 210 40% 98%;
  --secondary: 210 40% 96%;
  --secondary-foreground: 222.2 84% 4.9%;
  --muted: 210 40% 96%;
  --muted-foreground: 215.4 16.3% 46.9%;
  --accent: 210 40% 96%;
  --accent-foreground: 222.2 84% 4.9%;
  --destructive: 0 84.2% 60.2%;
  --destructive-foreground: 210 40% 98%;
  --border: 214.3 31.8% 91.4%;
  --input: 214.3 31.8% 91.4%;
  --ring: 221.2 83.2% 53.3%;
  --radius: 0.5rem;
}

.dark {
  --background: 222.2 84% 4.9%;
  --foreground: 210 40% 98%;
  --card: 222.2 84% 4.9%;
  --card-foreground: 210 40% 98%;
  --popover: 222.2 84% 4.9%;
  --popover-foreground: 210 40% 98%;
  --primary: 217.2 91.2% 59.8%;
  --primary-foreground: 222.2 84% 4.9%;
  --secondary: 217.2 32.6% 17.5%;
  --secondary-foreground: 210 40% 98%;
  --muted: 217.2 32.6% 17.5%;
  --muted-foreground: 215 20.2% 65.1%;
  --accent: 217.2 32.6% 17.5%;
  --accent-foreground: 210 40% 98%;
  --destructive: 0 62.8% 30.6%;
  --destructive-foreground: 210 40% 98%;
  --border: 217.2 32.6% 17.5%;
  --input: 217.2 32.6% 17.5%;
  --ring: 224.3 76.3% 94.1%;
}

* {
  @apply border-border;
}

body {
  @apply bg-background text-foreground;
  font-feature-settings: "rlig" 1, "calt" 1;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  @apply bg-muted;
}

::-webkit-scrollbar-thumb {
  @apply bg-muted-foreground rounded-md;
  opacity: 0.3;
}

::-webkit-scrollbar-thumb:hover {
  @apply bg-muted-foreground;
  opacity: 0.5;
}

/* Focus styles */
.focus-visible {
  @apply outline-none ring-2 ring-ring;
  ring-offset: 2px;
  ring-offset-color: hsl(var(--background));
}

/* Selection styles */
::selection {
  @apply bg-primary;
  opacity: 0.2;
}

/* Custom button variants */
.btn-primary {
  @apply bg-primary text-primary-foreground;
}

.btn-primary:hover {
  background-color: hsl(var(--primary));
  opacity: 0.9;
}

.btn-secondary {
  @apply bg-secondary text-secondary-foreground;
}

.btn-secondary:hover {
  background-color: hsl(var(--secondary));
  opacity: 0.8;
}

.btn-ghost:hover {
  @apply bg-accent text-accent-foreground;
}

/* Card styles */
.card {
  @apply bg-card text-card-foreground border border-border rounded-lg shadow-sm;
}

/* Input styles */
.input {
  @apply flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm;
  @apply placeholder-muted-foreground;
  @apply focus:outline-none focus:ring-2 focus:ring-ring;
  @apply disabled:cursor-not-allowed disabled:opacity-50;
}

.input:focus {
  ring-offset: 2px;
  ring-offset-color: hsl(var(--background));
}

/* Loading spinner */
.spinner {
  @apply animate-spin rounded-full border-2 border-muted border-t-primary;
}

/* Gradient backgrounds */
.gradient-primary {
  background: linear-gradient(135deg, hsl(var(--primary)) 0%, hsl(var(--primary)) 100%);
}

.gradient-secondary {
  background: linear-gradient(135deg, hsl(var(--secondary)) 0%, hsl(var(--muted)) 100%);
}

/* Glass morphism effect */
.glass {
  backdrop-filter: blur(12px);
  background-color: hsl(var(--background));
  opacity: 0.8;
  @apply border border-border;
  border-opacity: 0.5;
}

/* Hover animations */
.hover-lift {
  @apply transition-transform duration-200;
}

.hover-lift:hover {
  @apply transform scale-105;
}

.hover-glow {
  @apply transition-shadow duration-200;
}

.hover-glow:hover {
  @apply shadow-lg;
  box-shadow: 0 10px 15px -3px hsl(var(--primary) / 0.25), 0 4px 6px -2px hsl(var(--primary) / 0.25);
}

/* Text utilities */
.text-balance {
  text-wrap: balance;
}

/* Layout utilities */
.center {
  @apply flex items-center justify-center;
}

.center-x {
  @apply flex justify-center;
}

.center-y {
  @apply flex items-center;
}

/* Spacing utilities */
.space-y-px > * + * {
  margin-top: 1px;
}

/* Animation utilities */
.animate-in {
  @apply animate-fade-in;
}

.animate-slide-in {
  @apply animate-slide-in;
}

/* Responsive utilities */
.container-responsive {
  @apply container mx-auto px-4;
}

@screen sm {
  .container-responsive {
    @apply px-6;
  }
}

@screen lg {
  .container-responsive {
    @apply px-8;
  }
}

/* Debug utilities */
.debug-grid {
  background-image:
    linear-gradient(rgba(255, 0, 0, 0.1) 1px, transparent 1px),
    linear-gradient(90deg, rgba(255, 0, 0, 0.1) 1px, transparent 1px);
  background-size: 20px 20px;
}

/* Line clamp utilities for Tailwind v2 */
.line-clamp-1 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
}

.line-clamp-2 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
}

.line-clamp-3 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 3;
}

/* Print styles */
@media print {
  .no-print {
    display: none !important;
  }
}

/* Reduced motion */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}
