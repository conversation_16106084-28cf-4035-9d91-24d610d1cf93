import React, { useEffect } from 'react'
import { Routes, Route } from 'react-router-dom'
import { useAppStore, useTheme } from '@/store'
import { Layout } from '@/components/layout/Layout'
import { HomePage } from '@/pages/HomePage'
import { SearchPage } from '@/pages/SearchPage'
import { VideoPage } from '@/pages/VideoPage'
import { CategoryPage } from '@/pages/CategoryPage'
import { PerformerPage } from '@/pages/PerformerPage'
import { FavoritesPage } from '@/pages/FavoritesPage'
import { StatsPage } from '@/pages/StatsPage'
import { AdminPage } from '@/pages/AdminPage'
import { NotFoundPage } from '@/pages/NotFoundPage'

function App() {
  const { isDarkMode } = useTheme()
  const { setCategories, setFeaturedPerformers } = useAppStore()

  // Apply theme to document
  useEffect(() => {
    const root = document.documentElement
    if (isDarkMode) {
      root.classList.add('dark')
    } else {
      root.classList.remove('dark')
    }
  }, [isDarkMode])

  // Initialize app data
  useEffect(() => {
    const initializeApp = async () => {
      try {
        // Load initial data in parallel
        const [categoriesResponse, performersResponse] = await Promise.allSettled([
          import('@/services/api').then(({ getCategories }) => getCategories()),
          import('@/services/api').then(({ getPerformers }) => getPerformers({ featured: true, limit: 20 })),
        ])

        if (categoriesResponse.status === 'fulfilled') {
          setCategories(categoriesResponse.value.categories)
        }

        if (performersResponse.status === 'fulfilled') {
          setFeaturedPerformers(performersResponse.value.items)
        }
      } catch (error) {
        console.error('Failed to initialize app:', error)
      }
    }

    initializeApp()
  }, [setCategories, setFeaturedPerformers])

  return (
    <div className="min-h-screen bg-background text-foreground">
      <Routes>
        {/* Admin route - outside of main layout */}
        <Route path="/admin" element={<AdminPage />} />

        {/* Main routes with layout */}
        <Route path="/*" element={
          <Layout>
            <Routes>
              <Route path="/" element={<HomePage />} />
              <Route path="/search" element={<SearchPage />} />
              <Route path="/video/:id" element={<VideoPage />} />
              <Route path="/category/:slug" element={<CategoryPage />} />
              <Route path="/performer/:slug" element={<PerformerPage />} />
              <Route path="/favorites" element={<FavoritesPage />} />
              <Route path="/stats" element={<StatsPage />} />
              <Route path="*" element={<NotFoundPage />} />
            </Routes>
          </Layout>
        } />
      </Routes>
    </div>
  )
}

export default App
