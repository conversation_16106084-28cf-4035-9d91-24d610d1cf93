import React, { useState } from 'react'
import { Grid, List, SortAsc, SortDesc, Filter, X } from 'lucide-react'
import { Button } from '@/components/ui/Button'
import { Badge } from '@/components/ui/Badge'
import { VideoGrid } from '@/components/video/VideoGrid'
import { VideoList } from '@/components/video/VideoList'
import { LoadingSpinner } from '@/components/ui/LoadingSpinner'
import { ErrorMessage } from '@/components/ui/ErrorMessage'
import { FilterPanel } from './FilterPanel'
import { SearchAnalytics } from './SearchAnalytics'
import { SavedSearches } from './SavedSearches'
import { cn } from '@/utils/cn'
import type { Video, FilterState } from '@/types'

interface SearchResultsProps {
  query: string
  results: Video[]
  totalResults: number
  hasMore: boolean
  isLoading: boolean
  error: string | null
  filters: FilterState
  onFiltersChange: (filters: FilterState) => void
  onLoadMore: () => void
  onRetry: () => void
  searchTime?: number
  className?: string
}

export function SearchResults({
  query,
  results,
  totalResults,
  hasMore,
  isLoading,
  error,
  filters,
  onFiltersChange,
  onLoadMore,
  onRetry,
  searchTime,
  className,
}: SearchResultsProps) {
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid')
  const [showFilters, setShowFilters] = useState(false)

  const getActiveFilterCount = () => {
    let count = 0
    if (filters.categories.length > 0) count++
    if (filters.performers.length > 0) count++
    if (filters.minDuration || filters.maxDuration) count++
    if (filters.minRating) count++
    return count
  }

  const getActiveFilterTags = () => {
    const tags = []
    
    // Category filters
    filters.categories.forEach(category => {
      tags.push({
        type: 'category',
        label: category,
        value: category,
        onRemove: () => {
          const newCategories = filters.categories.filter(c => c !== category)
          onFiltersChange({ ...filters, categories: newCategories })
        }
      })
    })

    // Performer filters
    filters.performers.forEach(performer => {
      tags.push({
        type: 'performer',
        label: performer,
        value: performer,
        onRemove: () => {
          const newPerformers = filters.performers.filter(p => p !== performer)
          onFiltersChange({ ...filters, performers: newPerformers })
        }
      })
    })

    // Duration filter
    if (filters.minDuration || filters.maxDuration) {
      let label = 'Duration: '
      if (filters.minDuration && filters.maxDuration) {
        const minMin = Math.floor(filters.minDuration / 60)
        const maxMin = Math.floor(filters.maxDuration / 60)
        label += `${minMin}-${maxMin} min`
      } else if (filters.minDuration) {
        const minMin = Math.floor(filters.minDuration / 60)
        label += `${minMin}+ min`
      } else if (filters.maxDuration) {
        const maxMin = Math.floor(filters.maxDuration / 60)
        label += `< ${maxMin} min`
      }
      
      tags.push({
        type: 'duration',
        label,
        value: 'duration',
        onRemove: () => {
          onFiltersChange({ ...filters, minDuration: undefined, maxDuration: undefined })
        }
      })
    }

    // Rating filter
    if (filters.minRating) {
      tags.push({
        type: 'rating',
        label: `Rating: ${filters.minRating}+`,
        value: 'rating',
        onRemove: () => {
          onFiltersChange({ ...filters, minRating: undefined })
        }
      })
    }

    return tags
  }

  const clearAllFilters = () => {
    onFiltersChange({
      categories: [],
      performers: [],
      sortBy: 'date_added',
      sortOrder: 'desc',
    })
  }

  const handleLoadSavedSearch = (savedQuery: string, savedFilters: FilterState) => {
    // This would typically trigger a new search
    window.location.href = `/search?q=${encodeURIComponent(savedQuery)}`
  }

  if (!query) {
    return (
      <div className="text-center py-12 space-y-4">
        <div className="w-16 h-16 mx-auto bg-muted rounded-full flex items-center justify-center">
          <Filter className="h-8 w-8 text-muted-foreground" />
        </div>
        <div className="space-y-2">
          <h3 className="text-lg font-semibold">Start Your Search</h3>
          <p className="text-muted-foreground">
            Enter a search term to find videos
          </p>
        </div>
      </div>
    )
  }

  return (
    <div className={cn("space-y-6", className)}>
      {/* Results Header */}
      <div className="flex flex-col lg:flex-row lg:items-center justify-between gap-4">
        <div className="flex items-center gap-4">
          <div>
            {totalResults > 0 ? (
              <p className="text-sm text-muted-foreground">
                Showing {totalResults.toLocaleString()} results for "<strong>{query}</strong>"
              </p>
            ) : (
              <p className="text-sm text-muted-foreground">
                No results found for "<strong>{query}</strong>"
              </p>
            )}
          </div>
        </div>

        <div className="flex items-center gap-2">
          {/* View Mode Toggle */}
          <div className="flex items-center border rounded-md">
            <Button
              variant={viewMode === 'grid' ? 'default' : 'ghost'}
              size="sm"
              onClick={() => setViewMode('grid')}
              className="rounded-r-none"
            >
              <Grid className="h-4 w-4" />
            </Button>
            <Button
              variant={viewMode === 'list' ? 'default' : 'ghost'}
              size="sm"
              onClick={() => setViewMode('list')}
              className="rounded-l-none"
            >
              <List className="h-4 w-4" />
            </Button>
          </div>

          {/* Filter Toggle */}
          <Button
            variant={showFilters ? 'default' : 'outline'}
            size="sm"
            onClick={() => setShowFilters(!showFilters)}
          >
            <Filter className="mr-2 h-4 w-4" />
            Filters
            {getActiveFilterCount() > 0 && (
              <Badge variant="secondary" className="ml-2 text-xs">
                {getActiveFilterCount()}
              </Badge>
            )}
          </Button>
        </div>
      </div>

      {/* Active Filter Tags */}
      {getActiveFilterTags().length > 0 && (
        <div className="flex flex-wrap items-center gap-2">
          <span className="text-sm text-muted-foreground">Active filters:</span>
          {getActiveFilterTags().map((tag, index) => (
            <Badge
              key={`${tag.type}-${tag.value}-${index}`}
              variant="secondary"
              className="flex items-center gap-1"
            >
              {tag.label}
              <button
                onClick={tag.onRemove}
                className="ml-1 hover:bg-muted-foreground/20 rounded-full p-0.5"
              >
                <X className="h-3 w-3" />
              </button>
            </Badge>
          ))}
          <Button
            variant="ghost"
            size="sm"
            onClick={clearAllFilters}
            className="text-xs"
          >
            Clear all
          </Button>
        </div>
      )}

      {/* Search Analytics */}
      {query && (
        <SearchAnalytics
          query={query}
          totalResults={totalResults}
          searchTime={searchTime}
          appliedFilters={getActiveFilterCount()}
        />
      )}

      <div className="flex gap-6">
        {/* Sidebar */}
        {showFilters && (
          <div className="w-80 flex-shrink-0 space-y-6">
            <FilterPanel
              filters={filters}
              onFiltersChange={onFiltersChange}
              onClose={() => setShowFilters(false)}
            />

            <SavedSearches
              currentQuery={query}
              currentFilters={filters}
              onLoadSearch={handleLoadSavedSearch}
            />
          </div>
        )}

        {/* Results */}
        <div className="flex-1 min-w-0">
          {isLoading ? (
            <LoadingSpinner />
          ) : error ? (
            <ErrorMessage message={error} onRetry={onRetry} />
          ) : results.length > 0 ? (
            <div className="space-y-6">
              {viewMode === 'grid' ? (
                <VideoGrid videos={results} />
              ) : (
                <VideoList videos={results} />
              )}
              
              {hasMore && (
                <div className="text-center">
                  <Button variant="outline" onClick={onLoadMore}>
                    Load More Results
                  </Button>
                </div>
              )}
            </div>
          ) : (
            <div className="text-center py-12 space-y-4">
              <div className="w-16 h-16 mx-auto bg-muted rounded-full flex items-center justify-center">
                <Filter className="h-8 w-8 text-muted-foreground" />
              </div>
              <div className="space-y-2">
                <h3 className="text-lg font-semibold">No Results Found</h3>
                <p className="text-muted-foreground">
                  Try adjusting your search terms or filters
                </p>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}
