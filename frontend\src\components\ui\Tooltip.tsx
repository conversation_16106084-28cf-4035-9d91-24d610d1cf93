import React, { useState, useRef, useEffect } from 'react'
import { createPortal } from 'react-dom'
import { cn } from '@/utils/cn'

interface TooltipProps {
  content: React.ReactNode
  children: React.ReactNode
  side?: 'top' | 'bottom' | 'left' | 'right'
  align?: 'start' | 'center' | 'end'
  delay?: number
  disabled?: boolean
  className?: string
  rich?: boolean
  maxWidth?: number
}

export function Tooltip({
  content,
  children,
  side = 'top',
  align = 'center',
  delay = 500,
  disabled = false,
  className,
  rich = false,
  maxWidth = 300,
}: TooltipProps) {
  const [isVisible, setIsVisible] = useState(false)
  const [position, setPosition] = useState({ x: 0, y: 0 })
  const triggerRef = useRef<HTMLDivElement>(null)
  const tooltipRef = useRef<HTMLDivElement>(null)
  const timeoutRef = useRef<NodeJS.Timeout>()

  const showTooltip = () => {
    if (disabled || !content) return
    
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current)
    }
    
    timeoutRef.current = setTimeout(() => {
      setIsVisible(true)
      updatePosition()
    }, delay)
  }

  const hideTooltip = () => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current)
    }
    setIsVisible(false)
  }

  const updatePosition = () => {
    if (!triggerRef.current || !tooltipRef.current) return

    const triggerRect = triggerRef.current.getBoundingClientRect()
    const tooltipRect = tooltipRef.current.getBoundingClientRect()
    const viewport = {
      width: window.innerWidth,
      height: window.innerHeight,
    }

    let x = 0
    let y = 0

    // Calculate base position
    switch (side) {
      case 'top':
        x = triggerRect.left + triggerRect.width / 2 - tooltipRect.width / 2
        y = triggerRect.top - tooltipRect.height - 8
        break
      case 'bottom':
        x = triggerRect.left + triggerRect.width / 2 - tooltipRect.width / 2
        y = triggerRect.bottom + 8
        break
      case 'left':
        x = triggerRect.left - tooltipRect.width - 8
        y = triggerRect.top + triggerRect.height / 2 - tooltipRect.height / 2
        break
      case 'right':
        x = triggerRect.right + 8
        y = triggerRect.top + triggerRect.height / 2 - tooltipRect.height / 2
        break
    }

    // Adjust for alignment
    if (side === 'top' || side === 'bottom') {
      switch (align) {
        case 'start':
          x = triggerRect.left
          break
        case 'end':
          x = triggerRect.right - tooltipRect.width
          break
      }
    } else {
      switch (align) {
        case 'start':
          y = triggerRect.top
          break
        case 'end':
          y = triggerRect.bottom - tooltipRect.height
          break
      }
    }

    // Keep tooltip within viewport
    x = Math.max(8, Math.min(x, viewport.width - tooltipRect.width - 8))
    y = Math.max(8, Math.min(y, viewport.height - tooltipRect.height - 8))

    setPosition({ x, y })
  }

  useEffect(() => {
    if (isVisible) {
      updatePosition()
      
      const handleScroll = () => updatePosition()
      const handleResize = () => updatePosition()
      
      window.addEventListener('scroll', handleScroll, true)
      window.addEventListener('resize', handleResize)
      
      return () => {
        window.removeEventListener('scroll', handleScroll, true)
        window.removeEventListener('resize', handleResize)
      }
    }
  }, [isVisible])

  useEffect(() => {
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current)
      }
    }
  }, [])

  const tooltipContent = isVisible && content && (
    <div
      ref={tooltipRef}
      className={cn(
        "fixed z-50 px-3 py-2 text-sm rounded-md shadow-lg border",
        rich 
          ? "bg-card text-card-foreground border-border" 
          : "bg-popover text-popover-foreground border-border",
        "animate-in fade-in-0 zoom-in-95",
        className
      )}
      style={{
        left: position.x,
        top: position.y,
        maxWidth: maxWidth,
      }}
    >
      {content}
      
      {/* Arrow */}
      <div
        className={cn(
          "absolute w-2 h-2 rotate-45",
          rich ? "bg-card border-l border-t border-border" : "bg-popover border-l border-t border-border",
          {
            'bottom-[-4px] left-1/2 transform -translate-x-1/2': side === 'top',
            'top-[-4px] left-1/2 transform -translate-x-1/2': side === 'bottom',
            'right-[-4px] top-1/2 transform -translate-y-1/2': side === 'left',
            'left-[-4px] top-1/2 transform -translate-y-1/2': side === 'right',
          }
        )}
      />
    </div>
  )

  return (
    <>
      <div
        ref={triggerRef}
        onMouseEnter={showTooltip}
        onMouseLeave={hideTooltip}
        onFocus={showTooltip}
        onBlur={hideTooltip}
        className="inline-block"
      >
        {children}
      </div>
      
      {typeof document !== 'undefined' && createPortal(
        tooltipContent,
        document.body
      )}
    </>
  )
}

// Rich tooltip with more content
interface RichTooltipProps extends Omit<TooltipProps, 'content' | 'rich'> {
  title?: string
  description?: string
  shortcut?: string
  image?: string
  actions?: Array<{
    label: string
    action: () => void
  }>
}

export function RichTooltip({
  title,
  description,
  shortcut,
  image,
  actions,
  children,
  ...props
}: RichTooltipProps) {
  const content = (
    <div className="space-y-2 max-w-xs">
      {image && (
        <img
          src={image}
          alt={title}
          className="w-full h-24 object-cover rounded"
        />
      )}
      
      {title && (
        <div className="font-semibold text-sm">{title}</div>
      )}
      
      {description && (
        <div className="text-xs text-muted-foreground leading-relaxed">
          {description}
        </div>
      )}
      
      {shortcut && (
        <div className="flex items-center gap-2 pt-1 border-t">
          <span className="text-xs text-muted-foreground">Shortcut:</span>
          <kbd className="px-1.5 py-0.5 text-xs bg-muted rounded border">
            {shortcut}
          </kbd>
        </div>
      )}
      
      {actions && actions.length > 0 && (
        <div className="flex gap-1 pt-2 border-t">
          {actions.map((action, index) => (
            <button
              key={index}
              onClick={action.action}
              className="px-2 py-1 text-xs bg-primary text-primary-foreground rounded hover:bg-primary/90 transition-colors"
            >
              {action.label}
            </button>
          ))}
        </div>
      )}
    </div>
  )

  return (
    <Tooltip content={content} rich={true} {...props}>
      {children}
    </Tooltip>
  )
}

// Simple tooltip for quick use
export function SimpleTooltip({ 
  text, 
  children, 
  ...props 
}: { text: string; children: React.ReactNode } & Partial<TooltipProps>) {
  return (
    <Tooltip content={text} {...props}>
      {children}
    </Tooltip>
  )
}

// Hook for programmatic tooltips
export function useTooltip() {
  const [tooltip, setTooltip] = useState<{
    content: React.ReactNode
    position: { x: number; y: number }
    visible: boolean
  }>({
    content: null,
    position: { x: 0, y: 0 },
    visible: false,
  })

  const showTooltip = (content: React.ReactNode, event: MouseEvent) => {
    setTooltip({
      content,
      position: { x: event.clientX, y: event.clientY },
      visible: true,
    })
  }

  const hideTooltip = () => {
    setTooltip(prev => ({ ...prev, visible: false }))
  }

  const TooltipRenderer = () => {
    if (!tooltip.visible || !tooltip.content) return null

    return createPortal(
      <div
        className="fixed z-50 px-3 py-2 text-sm bg-popover text-popover-foreground border rounded-md shadow-lg animate-in fade-in-0 zoom-in-95"
        style={{
          left: tooltip.position.x + 10,
          top: tooltip.position.y - 10,
        }}
      >
        {tooltip.content}
      </div>,
      document.body
    )
  }

  return {
    showTooltip,
    hideTooltip,
    TooltipRenderer,
  }
}
