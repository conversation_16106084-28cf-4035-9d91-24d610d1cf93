/**
 * Admin API service for managing scraping sites, jobs, and users
 */

const API_BASE = 'http://localhost:8000/api/v1/admin';

export interface LoginRequest {
  username: string;
  password: string;
}

export interface LoginResponse {
  access_token: string;
  token_type: string;
  expires_in: number;
  user: AdminUser;
}

export interface AdminUser {
  id: number;
  username: string;
  email: string;
  role: 'owner' | 'admin' | 'moderator' | 'viewer';
  is_active: boolean;
  is_superuser: boolean;
  full_name?: string;
  avatar_url?: string;
  last_login_at?: string;
  login_count: number;
  permissions: string[];
  created_at: string;
}

export interface ScrapingSite {
  id: number;
  name: string;
  base_url: string;
  description?: string;
  is_active: boolean;
  priority: number;
  scraping_interval_hours: number;
  max_pages_per_scrape: number;
  site_type: 'video' | 'image' | 'mixed';
  content_rating: 'adult' | 'general' | 'mixed';
  language: string;
  country?: string;
  rate_limit_delay: number;
  requires_auth: boolean;
  last_scraped_at?: string;
  last_successful_scrape?: string;
  last_error?: string;
  total_videos_scraped: number;
  failed_scrape_count: number;
  average_video_quality?: number;
  success_rate: number;
  status: string;
  created_at: string;
  updated_at: string;
  created_by?: string;
}

export interface ScrapeJob {
  id: number;
  site_id: number;
  status: 'pending' | 'running' | 'completed' | 'failed' | 'cancelled';
  started_at?: string;
  completed_at?: string;
  duration_seconds?: number;
  pages_scraped: number;
  videos_found: number;
  videos_added: number;
  videos_updated: number;
  videos_skipped: number;
  error_message?: string;
  error_count: number;
  created_at: string;
}

export interface DashboardStats {
  site_stats: {
    total_sites: number;
    active_sites: number;
    inactive_sites: number;
    failing_sites: number;
    never_scraped_sites: number;
  };
  scraping_stats: {
    total_jobs: number;
    running_jobs: number;
    completed_jobs: number;
    failed_jobs: number;
    total_videos_scraped: number;
    average_success_rate: number;
  };
  recent_jobs: ScrapeJob[];
  top_performing_sites: ScrapingSite[];
}

export interface CreateSiteRequest {
  name: string;
  base_url: string;
  description?: string;
  is_active?: boolean;
  priority?: number;
  scraping_interval_hours?: number;
  max_pages_per_scrape?: number;
  site_type?: 'video' | 'image' | 'mixed';
  content_rating?: 'adult' | 'general' | 'mixed';
  language?: string;
  country?: string;
  rate_limit_delay?: number;
  requires_auth?: boolean;
  scraping_config?: Record<string, any>;
  headers?: Record<string, string>;
  auth_config?: Record<string, any>;
}

class AdminApiService {
  private token: string | null = null;

  constructor() {
    // Load token from localStorage on initialization
    this.token = localStorage.getItem('admin_token');
  }

  private getHeaders(): HeadersInit {
    const headers: HeadersInit = {
      'Content-Type': 'application/json',
    };

    if (this.token) {
      headers.Authorization = `Bearer ${this.token}`;
    }

    return headers;
  }

  private async handleResponse<T>(response: Response): Promise<T> {
    if (!response.ok) {
      if (response.status === 401) {
        this.logout();
        throw new Error('Authentication required');
      }
      const errorData = await response.json().catch(() => ({}));
      throw new Error(errorData.detail || `HTTP ${response.status}: ${response.statusText}`);
    }

    return response.json();
  }

  private async fetchWithTimeout(url: string, options: RequestInit = {}, timeout = 10000): Promise<Response> {
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), timeout);

    try {
      const response = await fetch(url, {
        ...options,
        signal: controller.signal,
      });
      clearTimeout(timeoutId);
      return response;
    } catch (error) {
      clearTimeout(timeoutId);
      if (error instanceof Error && error.name === 'AbortError') {
        throw new Error('Request timeout. Please check your connection.');
      }
      throw error;
    }
  }

  async login(credentials: LoginRequest): Promise<LoginResponse> {
    const response = await this.fetchWithTimeout(`${API_BASE}/login`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(credentials),
    });

    const data = await this.handleResponse<LoginResponse>(response);
    
    // Store token
    this.token = data.access_token;
    localStorage.setItem('admin_token', this.token);
    localStorage.setItem('admin_user', JSON.stringify(data.user));

    return data;
  }

  logout(): void {
    this.token = null;
    localStorage.removeItem('admin_token');
    localStorage.removeItem('admin_user');
  }

  isAuthenticated(): boolean {
    return !!this.token;
  }

  getCurrentUser(): AdminUser | null {
    const userData = localStorage.getItem('admin_user');
    return userData ? JSON.parse(userData) : null;
  }

  async getDashboardStats(): Promise<DashboardStats> {
    const response = await this.fetchWithTimeout(`${API_BASE}/dashboard`, {
      headers: this.getHeaders(),
    });
    return this.handleResponse<DashboardStats>(response);
  }

  async getSites(params?: {
    skip?: number;
    limit?: number;
    search?: string;
    status_filter?: string;
    sort_by?: string;
    sort_order?: string;
  }): Promise<{ sites: ScrapingSite[]; total: number; has_more: boolean }> {
    const searchParams = new URLSearchParams();
    if (params) {
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined) {
          searchParams.append(key, value.toString());
        }
      });
    }

    const response = await fetch(`${API_BASE}/sites?${searchParams}`, {
      headers: this.getHeaders(),
    });
    return this.handleResponse(response);
  }

  async createSite(siteData: CreateSiteRequest): Promise<ScrapingSite> {
    const response = await fetch(`${API_BASE}/sites`, {
      method: 'POST',
      headers: this.getHeaders(),
      body: JSON.stringify(siteData),
    });
    return this.handleResponse<ScrapingSite>(response);
  }

  async updateSite(siteId: number, siteData: Partial<CreateSiteRequest>): Promise<ScrapingSite> {
    const response = await fetch(`${API_BASE}/sites/${siteId}`, {
      method: 'PUT',
      headers: this.getHeaders(),
      body: JSON.stringify(siteData),
    });
    return this.handleResponse<ScrapingSite>(response);
  }

  async deleteSite(siteId: number): Promise<void> {
    const response = await fetch(`${API_BASE}/sites/${siteId}`, {
      method: 'DELETE',
      headers: this.getHeaders(),
    });
    await this.handleResponse(response);
  }

  async bulkSiteAction(action: 'activate' | 'deactivate' | 'delete' | 'scrape', siteIds: number[]): Promise<{
    success: boolean;
    message: string;
    affected_count: number;
    errors: string[];
  }> {
    const response = await fetch(`${API_BASE}/sites/bulk`, {
      method: 'POST',
      headers: this.getHeaders(),
      body: JSON.stringify({ action, site_ids: siteIds }),
    });
    return this.handleResponse(response);
  }

  async getJobs(params?: {
    skip?: number;
    limit?: number;
    site_id?: number;
    status_filter?: string;
    sort_by?: string;
    sort_order?: string;
  }): Promise<{ jobs: ScrapeJob[]; total: number; has_more: boolean }> {
    const searchParams = new URLSearchParams();
    if (params) {
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined) {
          searchParams.append(key, value.toString());
        }
      });
    }

    const response = await fetch(`${API_BASE}/jobs?${searchParams}`, {
      headers: this.getHeaders(),
    });
    return this.handleResponse(response);
  }

  async createJob(siteId: number): Promise<ScrapeJob> {
    const response = await fetch(`${API_BASE}/jobs`, {
      method: 'POST',
      headers: this.getHeaders(),
      body: JSON.stringify({ site_id: siteId }),
    });
    return this.handleResponse<ScrapeJob>(response);
  }

  async cancelJob(jobId: number): Promise<void> {
    const response = await fetch(`${API_BASE}/jobs/${jobId}/cancel`, {
      method: 'POST',
      headers: this.getHeaders(),
    });
    await this.handleResponse(response);
  }

  async getUsers(params?: {
    skip?: number;
    limit?: number;
    search?: string;
    role_filter?: string;
  }): Promise<{ users: AdminUser[]; total: number; has_more: boolean }> {
    const searchParams = new URLSearchParams();
    if (params) {
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined) {
          searchParams.append(key, value.toString());
        }
      });
    }

    const response = await fetch(`${API_BASE}/users?${searchParams}`, {
      headers: this.getHeaders(),
    });
    return this.handleResponse(response);
  }
}

export const adminApi = new AdminApiService();
