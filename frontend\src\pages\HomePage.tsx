import React from 'react'
import { <PERSON> } from 'react-router-dom'
import { useQuery } from '@tanstack/react-query'
import { TrendingUp, Clock, Star, ArrowRight } from 'lucide-react'
import { Button } from '@/components/ui/Button'
import { VideoGrid } from '@/components/video/VideoGrid'
import { StatsCard } from '@/components/stats/StatsCard'
import { CategoryGrid } from '@/components/category/CategoryGrid'
import { LoadingSpinner } from '@/components/ui/LoadingSpinner'
import { ErrorMessage } from '@/components/ui/ErrorMessage'
import { 
  getTrendingVideos, 
  getRandomVideos, 
  getStatsOverview,
  getCategories 
} from '@/services/api'

export function HomePage() {
  // Fetch data
  const { data: trendingVideos, isLoading: trendingLoading } = useQuery({
    queryKey: ['trending-videos'],
    queryFn: () => getTrendingVideos(8),
  })

  const { data: randomVideos, isLoading: randomLoading } = useQuery({
    queryKey: ['random-videos'],
    queryFn: () => getRandomVideos(8),
  })

  const { data: stats, isLoading: statsLoading } = useQuery({
    queryKey: ['stats-overview'],
    queryFn: getStatsOverview,
  })

  const { data: categoriesData, isLoading: categoriesLoading } = useQuery({
    queryKey: ['categories'],
    queryFn: getCategories,
  })

  const categories = categoriesData?.categories || []

  return (
    <div className="space-y-12">
      {/* Hero Section */}
      <section className="text-center space-y-6">
        <div className="space-y-4">
          <h1 className="text-4xl md:text-6xl font-bold bg-gradient-to-r from-primary to-primary/60 bg-clip-text text-transparent">
            Video Aggregator
          </h1>
          <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
            Discover and explore curated video content from across the web. 
            Find trending videos, explore categories, and build your personal collection.
          </p>
        </div>
        
        <div className="flex flex-col sm:flex-row gap-4 justify-center">
          <Button size="lg" asChild>
            <Link to="/search">
              Start Exploring
              <ArrowRight className="ml-2 h-4 w-4" />
            </Link>
          </Button>
          <Button variant="outline" size="lg" asChild>
            <Link to="/trending">
              <TrendingUp className="mr-2 h-4 w-4" />
              View Trending
            </Link>
          </Button>
        </div>
      </section>

      {/* Stats Overview */}
      {stats && !statsLoading && (
        <section className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <StatsCard
            title="Total Videos"
            value={stats.total_videos.toLocaleString()}
            icon={<Clock className="h-4 w-4" />}
            description="Videos in collection"
          />
          <StatsCard
            title="Categories"
            value={stats.total_categories.toString()}
            icon={<Star className="h-4 w-4" />}
            description="Different categories"
          />
          <StatsCard
            title="Total Duration"
            value={`${Math.round(stats.total_duration_hours)}h`}
            icon={<Clock className="h-4 w-4" />}
            description="Hours of content"
          />
          <StatsCard
            title="Average Rating"
            value={stats.average_rating.toFixed(1)}
            icon={<Star className="h-4 w-4" />}
            description="Out of 10"
          />
        </section>
      )}

      {/* Trending Videos */}
      <section className="space-y-6">
        <div className="flex items-center justify-between">
          <div>
            <h2 className="text-3xl font-bold">Trending Now</h2>
            <p className="text-muted-foreground">Most popular videos this week</p>
          </div>
          <Button variant="outline" asChild>
            <Link to="/trending">
              View All
              <ArrowRight className="ml-2 h-4 w-4" />
            </Link>
          </Button>
        </div>

        {trendingLoading ? (
          <LoadingSpinner />
        ) : trendingVideos?.items ? (
          <VideoGrid videos={trendingVideos.items} />
        ) : (
          <ErrorMessage message="Failed to load trending videos" />
        )}
      </section>

      {/* Categories */}
      <section className="space-y-6" data-tour="categories">
        <div className="flex items-center justify-between">
          <div>
            <h2 className="text-3xl font-bold">Browse Categories</h2>
            <p className="text-muted-foreground">Explore content by category</p>
          </div>
          <Button variant="outline" asChild>
            <Link to="/categories">
              View All
              <ArrowRight className="ml-2 h-4 w-4" />
            </Link>
          </Button>
        </div>

        {categoriesLoading ? (
          <LoadingSpinner />
        ) : categories.length > 0 ? (
          <CategoryGrid categories={categories.slice(0, 8)} />
        ) : (
          <ErrorMessage message="Failed to load categories" />
        )}
      </section>

      {/* Discovery Section */}
      <section className="space-y-6">
        <div className="flex items-center justify-between">
          <div>
            <h2 className="text-3xl font-bold">Discover Something New</h2>
            <p className="text-muted-foreground">Random high-quality videos for you</p>
          </div>
          <Button variant="outline" asChild>
            <Link to="/discovery">
              More Discoveries
              <ArrowRight className="ml-2 h-4 w-4" />
            </Link>
          </Button>
        </div>

        {randomLoading ? (
          <LoadingSpinner />
        ) : randomVideos?.items ? (
          <VideoGrid videos={randomVideos.items} />
        ) : (
          <ErrorMessage message="Failed to load discovery videos" />
        )}
      </section>

      {/* Call to Action */}
      <section className="text-center space-y-6 py-12 bg-muted/50 rounded-lg">
        <div className="space-y-4">
          <h2 className="text-3xl font-bold">Ready to Start Exploring?</h2>
          <p className="text-muted-foreground max-w-2xl mx-auto">
            Join thousands of users discovering amazing video content. 
            Create your favorites collection and get personalized recommendations.
          </p>
        </div>
        
        <div className="flex flex-col sm:flex-row gap-4 justify-center">
          <Button size="lg" asChild>
            <Link to="/search">
              Start Searching
            </Link>
          </Button>
          <Button variant="outline" size="lg" asChild>
            <Link to="/favorites">
              View Favorites
            </Link>
          </Button>
        </div>
      </section>
    </div>
  )
}
