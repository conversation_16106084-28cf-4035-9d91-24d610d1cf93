/**
 * Site template selector component for choosing pre-configured site templates
 */

import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../ui/Card';
import { Button } from '../ui/Button';
import { Badge } from '../ui/Badge';
import { Input } from '../ui/Input';
import { 
  Search, 
  Filter, 
  Sparkles,
  CheckCircle,
  Globe,
  Shield,
  Tv,
  Smartphone,
  Image,
  Palette
} from 'lucide-react';
import { siteTemplates, getTemplatesByCategory, SiteTemplate } from '../../data/siteTemplates';

interface SiteTemplateSelectorProps {
  isOpen: boolean;
  onClose: () => void;
  onSelectTemplate: (template: SiteTemplate) => void;
}

const categoryIcons = {
  adult: Shield,
  general: Globe,
  streaming: Tv,
  social: Smartphone
};

const categoryColors = {
  adult: 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300',
  general: 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300',
  streaming: 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-300',
  social: 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300'
};

export const SiteTemplateSelector: React.FC<SiteTemplateSelectorProps> = ({
  isOpen,
  onClose,
  onSelectTemplate
}) => {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [selectedTemplate, setSelectedTemplate] = useState<string | null>(null);

  if (!isOpen) return null;

  const filteredTemplates = siteTemplates.filter(template => {
    const matchesSearch = template.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         template.description.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesCategory = selectedCategory === 'all' || template.category === selectedCategory;
    return matchesSearch && matchesCategory;
  });

  const categories = [
    { id: 'all', name: 'All Templates', count: siteTemplates.length },
    { id: 'general', name: 'General', count: getTemplatesByCategory('general').length },
    { id: 'adult', name: 'Adult', count: getTemplatesByCategory('adult').length },
    { id: 'streaming', name: 'Streaming', count: getTemplatesByCategory('streaming').length },
    { id: 'social', name: 'Social', count: getTemplatesByCategory('social').length }
  ];

  const handleSelectTemplate = (template: SiteTemplate) => {
    setSelectedTemplate(template.id);
  };

  const handleUseTemplate = () => {
    const template = siteTemplates.find(t => t.id === selectedTemplate);
    if (template) {
      onSelectTemplate(template);
      onClose();
    }
  };

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
      <div className="bg-background rounded-lg shadow-xl max-w-6xl w-full max-h-[90vh] overflow-hidden">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b">
          <div>
            <h2 className="text-xl font-semibold flex items-center gap-2">
              <Sparkles className="h-5 w-5 text-primary" />
              Choose Site Template
            </h2>
            <p className="text-sm text-muted-foreground mt-1">
              Start with a pre-configured template for faster setup
            </p>
          </div>
          <Button variant="ghost" size="sm" onClick={onClose}>
            ×
          </Button>
        </div>

        {/* Content */}
        <div className="flex h-[calc(90vh-140px)]">
          {/* Sidebar */}
          <div className="w-64 border-r bg-muted/30 p-4">
            <div className="space-y-4">
              <div>
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                  <Input
                    placeholder="Search templates..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10"
                  />
                </div>
              </div>

              <div>
                <h3 className="text-sm font-medium mb-3 flex items-center gap-2">
                  <Filter className="h-4 w-4" />
                  Categories
                </h3>
                <div className="space-y-1">
                  {categories.map(category => (
                    <button
                      key={category.id}
                      onClick={() => setSelectedCategory(category.id)}
                      className={`w-full text-left px-3 py-2 rounded-md text-sm transition-colors ${
                        selectedCategory === category.id
                          ? 'bg-primary text-primary-foreground'
                          : 'hover:bg-muted'
                      }`}
                    >
                      <div className="flex items-center justify-between">
                        <span>{category.name}</span>
                        <Badge variant="outline" className="text-xs">
                          {category.count}
                        </Badge>
                      </div>
                    </button>
                  ))}
                </div>
              </div>
            </div>
          </div>

          {/* Templates Grid */}
          <div className="flex-1 p-6 overflow-y-auto">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {filteredTemplates.map(template => {
                const CategoryIcon = categoryIcons[template.category] || Globe;
                const isSelected = selectedTemplate === template.id;
                
                return (
                  <Card
                    key={template.id}
                    className={`cursor-pointer transition-all hover:shadow-md ${
                      isSelected ? 'ring-2 ring-primary shadow-md' : ''
                    }`}
                    onClick={() => handleSelectTemplate(template)}
                  >
                    <CardHeader className="pb-3">
                      <div className="flex items-start justify-between">
                        <div className="flex items-center gap-3">
                          <div className="text-2xl">{template.icon}</div>
                          <div>
                            <CardTitle className="text-base">{template.name}</CardTitle>
                            <div className="flex items-center gap-2 mt-1">
                              <CategoryIcon className="h-3 w-3" />
                              <Badge 
                                variant="outline" 
                                className={`text-xs ${categoryColors[template.category]}`}
                              >
                                {template.category}
                              </Badge>
                            </div>
                          </div>
                        </div>
                        {isSelected && (
                          <CheckCircle className="h-5 w-5 text-primary" />
                        )}
                      </div>
                    </CardHeader>
                    <CardContent>
                      <CardDescription className="text-sm mb-3">
                        {template.description}
                      </CardDescription>
                      <div className="space-y-2 text-xs text-muted-foreground">
                        <div className="flex justify-between">
                          <span>Content Type:</span>
                          <span className="capitalize">{template.config.site_type}</span>
                        </div>
                        <div className="flex justify-between">
                          <span>Scrape Interval:</span>
                          <span>{template.config.scraping_interval_hours}h</span>
                        </div>
                        <div className="flex justify-between">
                          <span>Rate Limit:</span>
                          <span>{template.config.rate_limit_delay}s</span>
                        </div>
                        <div className="flex justify-between">
                          <span>Auth Required:</span>
                          <span>{template.config.requires_auth ? 'Yes' : 'No'}</span>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                );
              })}
            </div>

            {filteredTemplates.length === 0 && (
              <div className="text-center py-12 text-muted-foreground">
                <Palette className="h-12 w-12 mx-auto mb-4 opacity-50" />
                <p className="text-lg font-medium mb-2">No templates found</p>
                <p>Try adjusting your search or category filter</p>
              </div>
            )}
          </div>
        </div>

        {/* Footer */}
        <div className="flex items-center justify-between p-6 border-t bg-muted/30">
          <div className="text-sm text-muted-foreground">
            {selectedTemplate ? (
              <>Selected: <strong>{siteTemplates.find(t => t.id === selectedTemplate)?.name}</strong></>
            ) : (
              'Select a template to continue'
            )}
          </div>
          <div className="flex items-center gap-3">
            <Button variant="outline" onClick={onClose}>
              Cancel
            </Button>
            <Button 
              onClick={handleUseTemplate}
              disabled={!selectedTemplate}
            >
              <Sparkles className="h-4 w-4 mr-2" />
              Use Template
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
};
