"""
Main FastAPI application for the Video Content Aggregation System.
"""

from fastapi import Fast<PERSON><PERSON>, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
import uvicorn
from contextlib import asynccontextmanager
import logging
from loguru import logger

from app.api import videos, search, stats, favorites, recommendations, admin
# from app.api import scraper, scheduler  # Temporarily disabled due to import issues
from app.models.database import init_db
# from app.scheduler.task_scheduler import start_scheduler, stop_scheduler  # Temporarily disabled
from app.middleware.logging import RequestLoggingMiddleware, APIMetricsMiddleware, api_metrics


@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan manager."""
    # Startup
    logger.info("Starting Video Content Aggregation System...")
    
    # Initialize database
    await init_db()
    logger.info("Database initialized")
    
    # Start scheduler (temporarily disabled)
    # await start_scheduler()
    logger.info("Task scheduler started (disabled for testing)")
    
    yield
    
    # Shutdown
    logger.info("Shutting down...")
    # await stop_scheduler()
    logger.info("Task scheduler stopped (disabled for testing)")


# Create FastAPI application
app = FastAPI(
    title="Video Content Aggregation API",
    description="REST API for video content aggregation system with automated scraping and quality filtering",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc",
    lifespan=lifespan
)

# Configure CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:3000", "http://127.0.0.1:3000"],  # React dev server
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Add custom middleware
app.add_middleware(RequestLoggingMiddleware, log_requests=True, log_responses=False)
app.add_middleware(APIMetricsMiddleware)


# Exception handlers
@app.exception_handler(HTTPException)
async def http_exception_handler(request, exc):
    """Handle HTTP exceptions with proper logging."""
    logger.error(f"HTTP {exc.status_code}: {exc.detail}")
    return JSONResponse(
        status_code=exc.status_code,
        content={"detail": exc.detail, "status_code": exc.status_code}
    )


@app.exception_handler(Exception)
async def general_exception_handler(request, exc):
    """Handle general exceptions."""
    logger.error(f"Unhandled exception: {str(exc)}")
    return JSONResponse(
        status_code=500,
        content={"detail": "Internal server error", "status_code": 500}
    )


# Health check endpoint
@app.get("/health")
async def health_check():
    """Health check endpoint."""
    return {"status": "healthy", "message": "Video Content Aggregation API is running"}


# API metrics endpoint
@app.get("/metrics")
async def get_api_metrics():
    """Get API performance metrics."""
    return api_metrics.get_metrics()


# Include API routers
app.include_router(videos.router, prefix="/api/v1/videos", tags=["videos"])
app.include_router(search.router, prefix="/api/v1/search", tags=["search"])
app.include_router(stats.router, prefix="/api/v1/stats", tags=["statistics"])
# app.include_router(scraper.router, prefix="/api/v1/scraper", tags=["scraper"])  # Temporarily disabled
app.include_router(favorites.router, prefix="/api/v1/favorites", tags=["favorites"])
app.include_router(recommendations.router, prefix="/api/v1/recommendations", tags=["recommendations"])
app.include_router(admin.router, prefix="/api/v1", tags=["admin"])
# app.include_router(scheduler.router, prefix="/api/v1/scheduler", tags=["scheduler"])  # Temporarily disabled


# Root endpoint
@app.get("/")
async def root():
    """Root endpoint with API information."""
    return {
        "message": "Video Content Aggregation API",
        "version": "1.0.0",
        "docs": "/docs",
        "health": "/health"
    }


if __name__ == "__main__":
    # Configure logging
    logging.basicConfig(level=logging.INFO)
    
    # Run the application
    uvicorn.run(
        "app.main:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    )
