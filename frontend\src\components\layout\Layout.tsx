import React from 'react'
import { <PERSON><PERSON> } from './Header'
import { Sidebar } from './Sidebar'
import { Footer } from './Footer'
import { NotificationSystem } from '@/components/notifications/NotificationSystem'
import { KeyboardShortcuts } from '@/components/ui/KeyboardShortcuts'
import { AccessibilityFeatures } from '@/components/accessibility/AccessibilityFeatures'
import { HelpSystem } from '@/components/help/HelpSystem'
import { OnboardingTour, homeTourSteps, useOnboarding } from '@/components/onboarding/OnboardingTour'
import { useAppStore } from '@/store'
import { cn } from '@/utils/cn'

interface LayoutProps {
  children: React.ReactNode
}

export function Layout({ children }: LayoutProps) {
  const { sidebarOpen } = useAppStore()
  const { hasCompletedOnboarding, currentTour, startTour, completeTour, skipTour } = useOnboarding()

  // Start onboarding tour for new users
  React.useEffect(() => {
    if (!hasCompletedOnboarding && !currentTour) {
      // Delay to ensure page is fully loaded
      setTimeout(() => {
        startTour('home')
      }, 1000)
    }
  }, [hasCompletedOnboarding, currentTour, startTour])

  return (
    <div className="min-h-screen flex flex-col">
      <Header />
      
      <div className="flex flex-1">
        <Sidebar />
        
        <main 
          className={cn(
            "flex-1 transition-all duration-300 ease-in-out",
            sidebarOpen ? "lg:ml-64" : "lg:ml-16"
          )}
        >
          <div className="container-responsive py-6">
            {children}
          </div>
        </main>
      </div>
      
      <Footer />

      {/* UX Enhancement Components */}
      <NotificationSystem />
      <KeyboardShortcuts />
      <AccessibilityFeatures />
      <HelpSystem />

      {/* Onboarding Tour */}
      {currentTour === 'home' && (
        <OnboardingTour
          steps={homeTourSteps}
          onComplete={completeTour}
          onSkip={skipTour}
        />
      )}
    </div>
  )
}
