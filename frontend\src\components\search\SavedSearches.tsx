import React, { useState } from 'react'
import { Bookmark, BookmarkCheck, Trash2, Search, Clock } from 'lucide-react'
import { Button } from '@/components/ui/Button'
import { Badge } from '@/components/ui/Badge'
import { useAppStore } from '@/store'
import { cn } from '@/utils/cn'
import type { FilterState } from '@/types'

interface SavedSearch {
  id: string
  name: string
  query: string
  filters: FilterState
  createdAt: Date
  lastUsed?: Date
  useCount: number
}

interface SavedSearchesProps {
  currentQuery: string
  currentFilters: FilterState
  onLoadSearch: (query: string, filters: FilterState) => void
  className?: string
}

export function SavedSearches({
  currentQuery,
  currentFilters,
  onLoadSearch,
  className,
}: SavedSearchesProps) {
  const { userPreferences, setUserPreferences } = useAppStore()
  const [showSaveDialog, setShowSaveDialog] = useState(false)
  const [searchName, setSearchName] = useState('')

  // Get saved searches from user preferences (in a real app, this would be in a separate store)
  const savedSearches: SavedSearch[] = (userPreferences as any).savedSearches || []

  const saveCurrentSearch = () => {
    if (!currentQuery.trim() || !searchName.trim()) return

    const newSearch: SavedSearch = {
      id: `search_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      name: searchName.trim(),
      query: currentQuery,
      filters: currentFilters,
      createdAt: new Date(),
      useCount: 0,
    }

    const updatedSearches = [...savedSearches, newSearch]
    setUserPreferences({
      ...userPreferences,
      savedSearches: updatedSearches,
    } as any)

    setSearchName('')
    setShowSaveDialog(false)
  }

  const deleteSearch = (searchId: string) => {
    const updatedSearches = savedSearches.filter(s => s.id !== searchId)
    setUserPreferences({
      ...userPreferences,
      savedSearches: updatedSearches,
    } as any)
  }

  const loadSearch = (search: SavedSearch) => {
    // Update use count and last used
    const updatedSearches = savedSearches.map(s =>
      s.id === search.id
        ? { ...s, useCount: s.useCount + 1, lastUsed: new Date() }
        : s
    )
    setUserPreferences({
      ...userPreferences,
      savedSearches: updatedSearches,
    } as any)

    onLoadSearch(search.query, search.filters)
  }

  const getFilterSummary = (filters: FilterState) => {
    const parts = []
    if (filters.categories.length > 0) {
      parts.push(`${filters.categories.length} categories`)
    }
    if (filters.performers.length > 0) {
      parts.push(`${filters.performers.length} performers`)
    }
    if (filters.minDuration || filters.maxDuration) {
      parts.push('duration filter')
    }
    if (filters.minRating) {
      parts.push(`rating ${filters.minRating}+`)
    }
    return parts.join(', ') || 'no filters'
  }

  const canSaveCurrentSearch = () => {
    return currentQuery.trim() && !savedSearches.some(s => 
      s.query === currentQuery && JSON.stringify(s.filters) === JSON.stringify(currentFilters)
    )
  }

  const formatDate = (date: Date) => {
    return new Date(date).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric'
    })
  }

  return (
    <div className={cn("space-y-4", className)}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <h3 className="font-semibold flex items-center gap-2">
          <Bookmark className="h-4 w-4" />
          Saved Searches
        </h3>
        
        {canSaveCurrentSearch() && (
          <Button
            variant="outline"
            size="sm"
            onClick={() => setShowSaveDialog(true)}
          >
            <BookmarkCheck className="mr-2 h-3 w-3" />
            Save Current
          </Button>
        )}
      </div>

      {/* Save Dialog */}
      {showSaveDialog && (
        <div className="bg-card border rounded-lg p-4 space-y-3">
          <div>
            <label className="text-sm font-medium">Search Name</label>
            <input
              type="text"
              value={searchName}
              onChange={(e) => setSearchName(e.target.value)}
              placeholder="Enter a name for this search..."
              className="w-full mt-1 px-3 py-2 border rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-ring"
              autoFocus
            />
          </div>
          
          <div className="text-xs text-muted-foreground">
            <p><strong>Query:</strong> "{currentQuery}"</p>
            <p><strong>Filters:</strong> {getFilterSummary(currentFilters)}</p>
          </div>
          
          <div className="flex gap-2">
            <Button size="sm" onClick={saveCurrentSearch} disabled={!searchName.trim()}>
              Save
            </Button>
            <Button variant="outline" size="sm" onClick={() => setShowSaveDialog(false)}>
              Cancel
            </Button>
          </div>
        </div>
      )}

      {/* Saved Searches List */}
      {savedSearches.length === 0 ? (
        <div className="text-center py-8 text-muted-foreground">
          <Bookmark className="h-8 w-8 mx-auto mb-2 opacity-50" />
          <p className="text-sm">No saved searches yet</p>
          <p className="text-xs">Save your frequent searches for quick access</p>
        </div>
      ) : (
        <div className="space-y-2">
          {savedSearches
            .sort((a, b) => (b.lastUsed || b.createdAt).getTime() - (a.lastUsed || a.createdAt).getTime())
            .map((search) => (
              <div
                key={search.id}
                className="bg-card border rounded-lg p-3 hover:shadow-sm transition-shadow"
              >
                <div className="flex items-start justify-between">
                  <div className="flex-1 min-w-0">
                    <button
                      onClick={() => loadSearch(search)}
                      className="text-left w-full group"
                    >
                      <div className="flex items-center gap-2 mb-1">
                        <h4 className="font-medium text-sm group-hover:text-primary transition-colors truncate">
                          {search.name}
                        </h4>
                        <Badge variant="outline" className="text-xs">
                          {search.useCount} uses
                        </Badge>
                      </div>
                      
                      <p className="text-xs text-muted-foreground mb-2 truncate">
                        "{search.query}"
                      </p>
                      
                      <div className="flex items-center gap-3 text-xs text-muted-foreground">
                        <span>{getFilterSummary(search.filters)}</span>
                        <span className="flex items-center gap-1">
                          <Clock className="h-3 w-3" />
                          {search.lastUsed ? `Used ${formatDate(search.lastUsed)}` : `Created ${formatDate(search.createdAt)}`}
                        </span>
                      </div>
                    </button>
                  </div>
                  
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => deleteSearch(search.id)}
                    className="opacity-0 group-hover:opacity-100 transition-opacity ml-2"
                  >
                    <Trash2 className="h-3 w-3" />
                  </Button>
                </div>
              </div>
            ))}
        </div>
      )}
    </div>
  )
}
