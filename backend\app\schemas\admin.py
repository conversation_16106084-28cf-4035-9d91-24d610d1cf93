"""
Pydantic schemas for admin panel functionality.
"""

from pydantic import BaseModel, Field, HttpUrl, validator
from typing import Optional, List, Dict, Any, Union
from datetime import datetime
from enum import Enum


class SiteType(str, Enum):
    VIDEO = "video"
    IMAGE = "image"
    MIXED = "mixed"


class ContentRating(str, Enum):
    ADULT = "adult"
    GENERAL = "general"
    MIXED = "mixed"


class UserRole(str, Enum):
    OWNER = "owner"
    ADMIN = "admin"
    MODERATOR = "moderator"
    VIEWER = "viewer"


class JobStatus(str, Enum):
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


# Scraping Site Schemas
class ScrapingSiteBase(BaseModel):
    name: str = Field(..., min_length=1, max_length=255)
    base_url: HttpUrl
    description: Optional[str] = None
    is_active: bool = True
    priority: int = Field(default=1, ge=1, le=10)
    scraping_interval_hours: int = Field(default=24, ge=1, le=168)  # Max 1 week
    max_pages_per_scrape: int = Field(default=10, ge=1, le=100)
    site_type: SiteType = SiteType.VIDEO
    content_rating: ContentRating = ContentRating.ADULT
    language: str = Field(default="en", min_length=2, max_length=10)
    country: Optional[str] = Field(None, min_length=2, max_length=10)
    rate_limit_delay: float = Field(default=1.0, ge=0.1, le=10.0)
    requires_auth: bool = False


class ScrapingSiteCreate(ScrapingSiteBase):
    scraping_config: Optional[Dict[str, Any]] = None
    headers: Optional[Dict[str, str]] = None
    auth_config: Optional[Dict[str, Any]] = None


class ScrapingSiteUpdate(BaseModel):
    name: Optional[str] = Field(None, min_length=1, max_length=255)
    base_url: Optional[HttpUrl] = None
    description: Optional[str] = None
    is_active: Optional[bool] = None
    priority: Optional[int] = Field(None, ge=1, le=10)
    scraping_interval_hours: Optional[int] = Field(None, ge=1, le=168)
    max_pages_per_scrape: Optional[int] = Field(None, ge=1, le=100)
    site_type: Optional[SiteType] = None
    content_rating: Optional[ContentRating] = None
    language: Optional[str] = Field(None, min_length=2, max_length=10)
    country: Optional[str] = Field(None, min_length=2, max_length=10)
    rate_limit_delay: Optional[float] = Field(None, ge=0.1, le=10.0)
    requires_auth: Optional[bool] = None
    scraping_config: Optional[Dict[str, Any]] = None
    headers: Optional[Dict[str, str]] = None
    auth_config: Optional[Dict[str, Any]] = None


class ScrapingSiteResponse(ScrapingSiteBase):
    id: int
    last_scraped_at: Optional[datetime] = None
    last_successful_scrape: Optional[datetime] = None
    last_error: Optional[str] = None
    total_videos_scraped: int = 0
    failed_scrape_count: int = 0
    average_video_quality: Optional[float] = None
    success_rate: float = 0.0
    status: str
    created_at: datetime
    updated_at: datetime
    created_by: Optional[str] = None

    class Config:
        from_attributes = True


class ScrapingSiteListResponse(BaseModel):
    sites: List[ScrapingSiteResponse]
    total: int
    skip: int
    limit: int
    has_more: bool


# Site Testing Schemas
class SiteTestRequest(BaseModel):
    url: HttpUrl
    headers: Optional[Dict[str, str]] = None
    auth_config: Optional[Dict[str, Any]] = None


class SiteTestResponse(BaseModel):
    success: bool
    message: str
    response_time: Optional[float] = None
    status_code: Optional[int] = None
    content_type: Optional[str] = None
    content_length: Optional[int] = None
    validation_results: Optional[Dict[str, Any]] = None


# Scrape Job Schemas
class ScrapeJobCreate(BaseModel):
    site_id: int
    scrape_config_override: Optional[Dict[str, Any]] = None


class ScrapeJobResponse(BaseModel):
    id: int
    site_id: int
    status: JobStatus
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    duration_seconds: Optional[float] = None
    pages_scraped: int = 0
    videos_found: int = 0
    videos_added: int = 0
    videos_updated: int = 0
    videos_skipped: int = 0
    error_message: Optional[str] = None
    error_count: int = 0
    created_at: datetime

    class Config:
        from_attributes = True


class ScrapeJobListResponse(BaseModel):
    jobs: List[ScrapeJobResponse]
    total: int
    skip: int
    limit: int
    has_more: bool


# Admin User Schemas
class AdminUserBase(BaseModel):
    username: str = Field(..., min_length=3, max_length=100)
    email: str = Field(..., pattern=r'^[^@]+@[^@]+\.[^@]+$')
    full_name: Optional[str] = Field(None, max_length=255)
    role: UserRole = UserRole.MODERATOR
    is_active: bool = True


class AdminUserCreate(AdminUserBase):
    password: str = Field(..., min_length=8, max_length=100)


class AdminUserUpdate(BaseModel):
    username: Optional[str] = Field(None, min_length=3, max_length=100)
    email: Optional[str] = Field(None, pattern=r'^[^@]+@[^@]+\.[^@]+$')
    full_name: Optional[str] = Field(None, max_length=255)
    role: Optional[UserRole] = None
    is_active: Optional[bool] = None
    password: Optional[str] = Field(None, min_length=8, max_length=100)


class AdminUserResponse(AdminUserBase):
    id: int
    is_superuser: bool = False
    avatar_url: Optional[str] = None
    last_login_at: Optional[datetime] = None
    login_count: int = 0
    permissions: List[str]
    created_at: datetime

    class Config:
        from_attributes = True


class AdminUserListResponse(BaseModel):
    users: List[AdminUserResponse]
    total: int
    skip: int
    limit: int
    has_more: bool


# Authentication Schemas
class LoginRequest(BaseModel):
    username: str
    password: str


class LoginResponse(BaseModel):
    access_token: str
    token_type: str = "bearer"
    expires_in: int
    user: AdminUserResponse


class TokenData(BaseModel):
    username: Optional[str] = None
    permissions: List[str] = []


# Dashboard Schemas
class SiteStats(BaseModel):
    total_sites: int
    active_sites: int
    inactive_sites: int
    failing_sites: int
    never_scraped_sites: int


class ScrapingStats(BaseModel):
    total_jobs: int
    running_jobs: int
    completed_jobs: int
    failed_jobs: int
    total_videos_scraped: int
    average_success_rate: float


class DashboardStats(BaseModel):
    site_stats: SiteStats
    scraping_stats: ScrapingStats
    recent_jobs: List[ScrapeJobResponse]
    top_performing_sites: List[ScrapingSiteResponse]


# Bulk Operations
class BulkSiteAction(BaseModel):
    action: str = Field(..., pattern=r'^(activate|deactivate|delete|scrape)$')
    site_ids: List[int] = Field(..., min_items=1)


class BulkActionResponse(BaseModel):
    success: bool
    message: str
    affected_count: int
    errors: List[str] = []


# Site Testing
class SiteTestRequest(BaseModel):
    site_id: int
    test_pages: int = Field(default=1, ge=1, le=5)


class SiteTestResponse(BaseModel):
    success: bool
    message: str
    test_results: Dict[str, Any]
    videos_found: int = 0
    errors: List[str] = []


# Configuration Templates
class ScrapingConfigTemplate(BaseModel):
    name: str
    description: str
    config: Dict[str, Any]
    site_type: SiteType
    example_url: str


class ConfigTemplateListResponse(BaseModel):
    templates: List[ScrapingConfigTemplate]


# Analytics
class SitePerformanceMetrics(BaseModel):
    site_id: int
    site_name: str
    total_scrapes: int
    successful_scrapes: int
    failed_scrapes: int
    success_rate: float
    average_videos_per_scrape: float
    last_scrape_date: Optional[datetime]
    total_videos_scraped: int


class PerformanceAnalytics(BaseModel):
    metrics: List[SitePerformanceMetrics]
    date_range: str
    total_sites_analyzed: int
