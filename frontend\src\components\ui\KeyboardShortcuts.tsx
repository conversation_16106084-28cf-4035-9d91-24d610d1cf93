import React, { useState, useEffect } from 'react'
import { Keyboard, Command, Search, Heart, Home, Settings, HelpCircle, X } from 'lucide-react'
import { Button } from '@/components/ui/Button'
import { Badge } from '@/components/ui/Badge'
import { cn } from '@/utils/cn'

interface Shortcut {
  id: string
  keys: string[]
  description: string
  category: 'navigation' | 'search' | 'playback' | 'general'
  action: () => void
}

interface KeyboardShortcutsProps {
  onClose?: () => void
  className?: string
}

export function KeyboardShortcuts({ onClose, className }: KeyboardShortcutsProps) {
  const [isVisible, setIsVisible] = useState(false)

  const shortcuts: Shortcut[] = [
    // Navigation
    { id: 'home', keys: ['g', 'h'], description: 'Go to Home', category: 'navigation', action: () => window.location.href = '/' },
    { id: 'search', keys: ['g', 's'], description: 'Go to Search', category: 'navigation', action: () => window.location.href = '/search' },
    { id: 'favorites', keys: ['g', 'f'], description: 'Go to Favorites', category: 'navigation', action: () => window.location.href = '/favorites' },
    { id: 'stats', keys: ['g', 't'], description: 'Go to Statistics', category: 'navigation', action: () => window.location.href = '/statistics' },
    
    // Search
    { id: 'focus-search', keys: ['/'], description: 'Focus search bar', category: 'search', action: () => focusSearchBar() },
    { id: 'clear-search', keys: ['Escape'], description: 'Clear search', category: 'search', action: () => clearSearch() },
    { id: 'next-suggestion', keys: ['ArrowDown'], description: 'Next search suggestion', category: 'search', action: () => {} },
    { id: 'prev-suggestion', keys: ['ArrowUp'], description: 'Previous search suggestion', category: 'search', action: () => {} },
    
    // General
    { id: 'toggle-theme', keys: ['t'], description: 'Toggle theme', category: 'general', action: () => toggleTheme() },
    { id: 'toggle-favorites', keys: ['f'], description: 'Toggle favorites', category: 'general', action: () => {} },
    { id: 'refresh', keys: ['r'], description: 'Refresh page', category: 'general', action: () => window.location.reload() },
    { id: 'help', keys: ['?'], description: 'Show keyboard shortcuts', category: 'general', action: () => setIsVisible(true) },
    { id: 'settings', keys: [','], description: 'Open settings', category: 'general', action: () => {} },
    
    // Playback (when video is focused)
    { id: 'play-pause', keys: ['Space'], description: 'Play/Pause video', category: 'playback', action: () => {} },
    { id: 'mute', keys: ['m'], description: 'Mute/Unmute', category: 'playback', action: () => {} },
    { id: 'fullscreen', keys: ['f'], description: 'Toggle fullscreen', category: 'playback', action: () => {} },
    { id: 'volume-up', keys: ['ArrowUp'], description: 'Volume up', category: 'playback', action: () => {} },
    { id: 'volume-down', keys: ['ArrowDown'], description: 'Volume down', category: 'playback', action: () => {} },
    { id: 'seek-forward', keys: ['ArrowRight'], description: 'Seek forward 10s', category: 'playback', action: () => {} },
    { id: 'seek-backward', keys: ['ArrowLeft'], description: 'Seek backward 10s', category: 'playback', action: () => {} },
  ]

  const categories = {
    navigation: { label: 'Navigation', icon: Home, color: 'text-blue-500' },
    search: { label: 'Search', icon: Search, color: 'text-green-500' },
    playback: { label: 'Playback', icon: Command, color: 'text-purple-500' },
    general: { label: 'General', icon: Settings, color: 'text-orange-500' },
  }

  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      // Don't trigger shortcuts when typing in inputs
      if (event.target instanceof HTMLInputElement || event.target instanceof HTMLTextAreaElement) {
        return
      }

      const key = event.key
      const isCtrl = event.ctrlKey || event.metaKey
      const isShift = event.shiftKey
      const isAlt = event.altKey

      // Handle special key combinations
      if (key === '?' && !isCtrl && !isShift && !isAlt) {
        event.preventDefault()
        setIsVisible(true)
        return
      }

      if (key === 'Escape') {
        event.preventDefault()
        setIsVisible(false)
        return
      }

      // Handle single key shortcuts
      const shortcut = shortcuts.find(s => 
        s.keys.length === 1 && s.keys[0] === key && !isCtrl && !isShift && !isAlt
      )

      if (shortcut) {
        event.preventDefault()
        shortcut.action()
      }
    }

    document.addEventListener('keydown', handleKeyDown)
    return () => document.removeEventListener('keydown', handleKeyDown)
  }, [shortcuts])

  const focusSearchBar = () => {
    const searchInput = document.querySelector('input[type="search"], input[placeholder*="search" i]') as HTMLInputElement
    if (searchInput) {
      searchInput.focus()
    }
  }

  const clearSearch = () => {
    const searchInput = document.querySelector('input[type="search"], input[placeholder*="search" i]') as HTMLInputElement
    if (searchInput) {
      searchInput.value = ''
      searchInput.dispatchEvent(new Event('input', { bubbles: true }))
    }
  }

  const toggleTheme = () => {
    const html = document.documentElement
    const isDark = html.classList.contains('dark')
    if (isDark) {
      html.classList.remove('dark')
      localStorage.setItem('theme', 'light')
    } else {
      html.classList.add('dark')
      localStorage.setItem('theme', 'dark')
    }
  }

  const formatKeys = (keys: string[]) => {
    return keys.map(key => {
      switch (key) {
        case 'ArrowUp': return '↑'
        case 'ArrowDown': return '↓'
        case 'ArrowLeft': return '←'
        case 'ArrowRight': return '→'
        case 'Space': return 'Space'
        case 'Escape': return 'Esc'
        case 'Enter': return '↵'
        default: return key
      }
    }).join(' + ')
  }

  if (!isVisible) {
    return (
      <button
        onClick={() => setIsVisible(true)}
        className="fixed bottom-4 right-4 p-3 bg-primary text-primary-foreground rounded-full shadow-lg hover:shadow-xl transition-all z-50"
        title="Keyboard shortcuts (?)"
      >
        <Keyboard className="h-5 w-5" />
      </button>
    )
  }

  return (
    <>
      {/* Backdrop */}
      <div
        className="fixed inset-0 bg-black/50 z-50"
        onClick={() => setIsVisible(false)}
      />
      
      {/* Modal */}
      <div className={cn(
        "fixed top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 z-50",
        "bg-card border rounded-lg shadow-xl max-w-4xl w-full mx-4 max-h-[80vh] overflow-hidden",
        className
      )}>
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-primary/10 rounded-lg">
              <Keyboard className="h-5 w-5 text-primary" />
            </div>
            <div>
              <h2 className="text-xl font-semibold">Keyboard Shortcuts</h2>
              <p className="text-sm text-muted-foreground">
                Navigate faster with these keyboard shortcuts
              </p>
            </div>
          </div>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setIsVisible(false)}
          >
            <X className="h-4 w-4" />
          </Button>
        </div>

        {/* Content */}
        <div className="p-6 overflow-y-auto max-h-[60vh]">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            {Object.entries(categories).map(([categoryKey, category]) => {
              const categoryShortcuts = shortcuts.filter(s => s.category === categoryKey)
              const Icon = category.icon
              
              return (
                <div key={categoryKey} className="space-y-4">
                  <div className="flex items-center gap-2">
                    <Icon className={cn("h-4 w-4", category.color)} />
                    <h3 className="font-semibold">{category.label}</h3>
                  </div>
                  
                  <div className="space-y-2">
                    {categoryShortcuts.map((shortcut) => (
                      <div
                        key={shortcut.id}
                        className="flex items-center justify-between p-2 rounded hover:bg-muted/50 transition-colors"
                      >
                        <span className="text-sm">{shortcut.description}</span>
                        <div className="flex gap-1">
                          {shortcut.keys.map((key, index) => (
                            <React.Fragment key={index}>
                              {index > 0 && (
                                <span className="text-xs text-muted-foreground mx-1">+</span>
                              )}
                              <Badge
                                variant="outline"
                                className="text-xs font-mono px-2 py-1 min-w-0"
                              >
                                {formatKeys([key])}
                              </Badge>
                            </React.Fragment>
                          ))}
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )
            })}
          </div>
        </div>

        {/* Footer */}
        <div className="flex items-center justify-between p-6 border-t bg-muted/50">
          <div className="flex items-center gap-2 text-sm text-muted-foreground">
            <HelpCircle className="h-4 w-4" />
            <span>Press <Badge variant="outline" className="text-xs">?</Badge> anytime to show this dialog</span>
          </div>
          <div className="flex gap-2">
            <Button variant="outline" size="sm">
              Print Shortcuts
            </Button>
            <Button size="sm" onClick={() => setIsVisible(false)}>
              Got it
            </Button>
          </div>
        </div>
      </div>
    </>
  )
}

// Hook for using keyboard shortcuts in components
export function useKeyboardShortcuts(shortcuts: Array<{
  keys: string[]
  action: () => void
  enabled?: boolean
}>) {
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      // Don't trigger shortcuts when typing in inputs
      if (event.target instanceof HTMLInputElement || event.target instanceof HTMLTextAreaElement) {
        return
      }

      shortcuts.forEach(shortcut => {
        if (shortcut.enabled === false) return

        // Simple key matching for now
        if (shortcut.keys.length === 1 && shortcut.keys[0] === event.key) {
          event.preventDefault()
          shortcut.action()
        }
      })
    }

    document.addEventListener('keydown', handleKeyDown)
    return () => document.removeEventListener('keydown', handleKeyDown)
  }, [shortcuts])
}
