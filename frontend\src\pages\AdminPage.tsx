/**
 * Admin page component - main entry point for admin functionality
 */

import React from 'react';
import { AdminAuthProvider, useAdminAuth } from '../contexts/AdminAuthContext';
import { AdminLogin } from '../components/admin/AdminLogin';
import { AdminDashboard } from '../components/admin/AdminDashboard';

const AdminPageContent: React.FC = () => {
  const { isAuthenticated, isLoading } = useAdminAuth();

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
          <p>Loading...</p>
        </div>
      </div>
    );
  }

  return isAuthenticated ? <AdminDashboard /> : <AdminLogin />;
};

export const AdminPage: React.FC = () => {
  return (
    <AdminAuthProvider>
      <AdminPageContent />
    </AdminAuthProvider>
  );
};
