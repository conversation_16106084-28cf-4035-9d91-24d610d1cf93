"""
Admin panel API endpoints for managing scraping sites and users.
"""

from fastapi import APIRouter, HTTPException, Query, Depends, status
from fastapi.security import H<PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from sqlalchemy.orm import Session
from sqlalchemy import desc, asc, func, and_, or_
from typing import List, Optional
import logging
from datetime import datetime, timedelta
import hashlib
import secrets

from app.models.database import get_db
from app.models.scraping_site import ScrapingSite, ScrapeJob, AdminUser
from app.schemas.admin import (
    ScrapingSiteCreate, ScrapingSiteUpdate, ScrapingSiteResponse, ScrapingSiteListResponse,
    ScrapeJobCreate, ScrapeJobResponse, ScrapeJobListResponse,
    AdminUserCreate, AdminUserUpdate, AdminUserResponse, AdminUserListResponse,
    LoginRequest, LoginResponse, DashboardStats, SiteStats, ScrapingStats,
    BulkSiteAction, BulkActionResponse, SiteTestRequest, SiteTestResponse,
    JobStatus, UserRole
)
import requests
import time

logger = logging.getLogger(__name__)
security = HTTPBearer()

router = APIRouter(prefix="/admin", tags=["admin"])


# Authentication and Authorization
def hash_password(password: str) -> str:
    """Hash a password using SHA-256 with salt."""
    salt = secrets.token_hex(16)
    return hashlib.sha256((password + salt).encode()).hexdigest() + ":" + salt


def verify_password(password: str, hashed: str) -> bool:
    """Verify a password against its hash."""
    try:
        hash_part, salt = hashed.split(":")
        return hashlib.sha256((password + salt).encode()).hexdigest() == hash_part
    except ValueError:
        return False


def get_current_user(
    credentials: HTTPAuthorizationCredentials = Depends(security),
    db: Session = Depends(get_db)
) -> AdminUser:
    """Get current authenticated admin user."""
    # In a real implementation, you'd verify JWT tokens here
    # For now, we'll use a simple token-based auth
    token = credentials.credentials
    
    # Simple token validation (in production, use proper JWT)
    if not token or len(token) < 10:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid authentication credentials",
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    # For demo purposes, assume token contains username
    # In production, decode JWT and extract user info
    username = token.split("_")[0] if "_" in token else "admin"
    
    user = db.query(AdminUser).filter(AdminUser.username == username).first()
    if not user or not user.is_active:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="User not found or inactive",
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    return user


def require_permission(permission: str):
    """Decorator to require specific permission."""
    def permission_checker(current_user: AdminUser = Depends(get_current_user)):
        if not current_user.has_permission(permission):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail=f"Permission '{permission}' required"
            )
        return current_user
    return permission_checker


# Authentication Endpoints
@router.post("/login", response_model=LoginResponse)
async def login(login_data: LoginRequest, db: Session = Depends(get_db)):
    """Authenticate admin user and return access token."""
    try:
        user = db.query(AdminUser).filter(
            AdminUser.username == login_data.username,
            AdminUser.is_active == True
        ).first()

        if not user or not verify_password(login_data.password, user.hashed_password):
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Incorrect username or password"
            )
        
        # Update login tracking
        user.last_login_at = datetime.utcnow()
        user.login_count += 1
        db.commit()
        
        # Generate simple token (in production, use proper JWT)
        access_token = f"{user.username}_{secrets.token_urlsafe(32)}"
        
        return LoginResponse(
            access_token=access_token,
            expires_in=3600 * 24,  # 24 hours
            user=AdminUserResponse.model_validate(user)
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Login error: {e}")
        raise HTTPException(status_code=500, detail="Login failed")


# Dashboard Endpoints
@router.get("/dashboard", response_model=DashboardStats)
async def get_dashboard_stats(
    current_user: AdminUser = Depends(require_permission("view_analytics")),
    db: Session = Depends(get_db)
):
    """Get dashboard statistics and overview."""
    try:
        # Site statistics
        total_sites = db.query(ScrapingSite).count()
        active_sites = db.query(ScrapingSite).filter(ScrapingSite.is_active == True).count()
        inactive_sites = total_sites - active_sites
        failing_sites = db.query(ScrapingSite).filter(ScrapingSite.failed_scrape_count > 5).count()
        never_scraped = db.query(ScrapingSite).filter(ScrapingSite.last_scraped_at.is_(None)).count()
        
        site_stats = SiteStats(
            total_sites=total_sites,
            active_sites=active_sites,
            inactive_sites=inactive_sites,
            failing_sites=failing_sites,
            never_scraped_sites=never_scraped
        )
        
        # Scraping statistics
        total_jobs = db.query(ScrapeJob).count()
        running_jobs = db.query(ScrapeJob).filter(ScrapeJob.status == JobStatus.RUNNING).count()
        completed_jobs = db.query(ScrapeJob).filter(ScrapeJob.status == JobStatus.COMPLETED).count()
        failed_jobs = db.query(ScrapeJob).filter(ScrapeJob.status == JobStatus.FAILED).count()
        
        total_videos_scraped = db.query(func.sum(ScrapingSite.total_videos_scraped)).scalar() or 0
        avg_success_rate = db.query(func.avg(ScrapingSite.success_rate)).scalar() or 0.0
        
        scraping_stats = ScrapingStats(
            total_jobs=total_jobs,
            running_jobs=running_jobs,
            completed_jobs=completed_jobs,
            failed_jobs=failed_jobs,
            total_videos_scraped=total_videos_scraped,
            average_success_rate=round(avg_success_rate, 2)
        )
        
        # Recent jobs
        recent_jobs = db.query(ScrapeJob).order_by(desc(ScrapeJob.created_at)).limit(10).all()
        
        # Top performing sites
        top_sites = db.query(ScrapingSite).filter(
            ScrapingSite.is_active == True
        ).order_by(desc(ScrapingSite.total_videos_scraped)).limit(5).all()
        
        return DashboardStats(
            site_stats=site_stats,
            scraping_stats=scraping_stats,
            recent_jobs=[ScrapeJobResponse.model_validate(job) for job in recent_jobs],
            top_performing_sites=[ScrapingSiteResponse.model_validate(site) for site in top_sites]
        )
        
    except Exception as e:
        logger.error(f"Dashboard stats error: {e}")
        raise HTTPException(status_code=500, detail="Failed to fetch dashboard stats")


# Scraping Sites Management
@router.get("/sites", response_model=ScrapingSiteListResponse)
async def get_scraping_sites(
    skip: int = Query(0, ge=0),
    limit: int = Query(50, ge=1, le=100),
    search: Optional[str] = Query(None),
    status_filter: Optional[str] = Query(None),
    sort_by: Optional[str] = Query("created_at"),
    sort_order: Optional[str] = Query("desc"),
    current_user: AdminUser = Depends(require_permission("manage_sites")),
    db: Session = Depends(get_db)
):
    """Get paginated list of scraping sites."""
    try:
        query = db.query(ScrapingSite)
        
        # Apply search filter
        if search:
            query = query.filter(
                or_(
                    ScrapingSite.name.ilike(f"%{search}%"),
                    ScrapingSite.base_url.ilike(f"%{search}%"),
                    ScrapingSite.description.ilike(f"%{search}%")
                )
            )
        
        # Apply status filter
        if status_filter:
            if status_filter == "active":
                query = query.filter(ScrapingSite.is_active == True)
            elif status_filter == "inactive":
                query = query.filter(ScrapingSite.is_active == False)
            elif status_filter == "failing":
                query = query.filter(ScrapingSite.failed_scrape_count > 5)
            elif status_filter == "never_scraped":
                query = query.filter(ScrapingSite.last_scraped_at.is_(None))
        
        # Apply sorting
        sort_column = getattr(ScrapingSite, sort_by, ScrapingSite.created_at)
        if sort_order.lower() == "asc":
            query = query.order_by(asc(sort_column))
        else:
            query = query.order_by(desc(sort_column))
        
        # Get total count
        total = query.count()
        
        # Apply pagination
        sites = query.offset(skip).limit(limit).all()
        
        return ScrapingSiteListResponse(
            sites=[ScrapingSiteResponse.model_validate(site) for site in sites],
            total=total,
            skip=skip,
            limit=limit,
            has_more=(skip + limit) < total
        )
        
    except Exception as e:
        logger.error(f"Error fetching scraping sites: {e}")
        raise HTTPException(status_code=500, detail="Failed to fetch scraping sites")


@router.post("/sites", response_model=ScrapingSiteResponse)
async def create_scraping_site(
    site_data: ScrapingSiteCreate,
    current_user: AdminUser = Depends(require_permission("manage_sites")),
    db: Session = Depends(get_db)
):
    """Create a new scraping site."""
    try:
        # Check if site with same URL already exists
        existing_site = db.query(ScrapingSite).filter(
            ScrapingSite.base_url == str(site_data.base_url)
        ).first()
        
        if existing_site:
            raise HTTPException(
                status_code=400,
                detail="Site with this URL already exists"
            )
        
        # Create new site
        site = ScrapingSite(
            name=site_data.name,
            base_url=str(site_data.base_url),
            description=site_data.description,
            is_active=site_data.is_active,
            priority=site_data.priority,
            scraping_interval_hours=site_data.scraping_interval_hours,
            max_pages_per_scrape=site_data.max_pages_per_scrape,
            site_type=site_data.site_type,
            content_rating=site_data.content_rating,
            language=site_data.language,
            country=site_data.country,
            rate_limit_delay=site_data.rate_limit_delay,
            requires_auth=site_data.requires_auth,
            scraping_config=site_data.scraping_config,
            headers=site_data.headers,
            auth_config=site_data.auth_config,
            created_by=current_user.username
        )
        
        db.add(site)
        db.commit()
        db.refresh(site)
        
        logger.info(f"Created scraping site: {site.name} by {current_user.username}")
        return ScrapingSiteResponse.model_validate(site)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error creating scraping site: {e}")
        db.rollback()
        raise HTTPException(status_code=500, detail="Failed to create scraping site")


@router.get("/sites/{site_id}", response_model=ScrapingSiteResponse)
async def get_scraping_site(
    site_id: int,
    current_user: AdminUser = Depends(require_permission("manage_sites")),
    db: Session = Depends(get_db)
):
    """Get a specific scraping site by ID."""
    try:
        site = db.query(ScrapingSite).filter(ScrapingSite.id == site_id).first()
        
        if not site:
            raise HTTPException(status_code=404, detail="Scraping site not found")
        
        return ScrapingSiteResponse.model_validate(site)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error fetching scraping site {site_id}: {e}")
        raise HTTPException(status_code=500, detail="Failed to fetch scraping site")


@router.put("/sites/{site_id}", response_model=ScrapingSiteResponse)
async def update_scraping_site(
    site_id: int,
    site_data: ScrapingSiteUpdate,
    current_user: AdminUser = Depends(require_permission("manage_sites")),
    db: Session = Depends(get_db)
):
    """Update a scraping site."""
    try:
        site = db.query(ScrapingSite).filter(ScrapingSite.id == site_id).first()
        
        if not site:
            raise HTTPException(status_code=404, detail="Scraping site not found")
        
        # Update fields
        update_data = site_data.model_dump(exclude_unset=True)
        for field, value in update_data.items():
            if field == "base_url" and value:
                value = str(value)
            setattr(site, field, value)
        
        site.updated_at = datetime.utcnow()
        db.commit()
        db.refresh(site)
        
        logger.info(f"Updated scraping site {site_id} by {current_user.username}")
        return ScrapingSiteResponse.model_validate(site)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error updating scraping site {site_id}: {e}")
        db.rollback()
        raise HTTPException(status_code=500, detail="Failed to update scraping site")


@router.delete("/sites/{site_id}")
async def delete_scraping_site(
    site_id: int,
    current_user: AdminUser = Depends(require_permission("manage_sites")),
    db: Session = Depends(get_db)
):
    """Delete a scraping site."""
    try:
        site = db.query(ScrapingSite).filter(ScrapingSite.id == site_id).first()
        
        if not site:
            raise HTTPException(status_code=404, detail="Scraping site not found")
        
        # Check if there are running jobs for this site
        running_jobs = db.query(ScrapeJob).filter(
            ScrapeJob.site_id == site_id,
            ScrapeJob.status == JobStatus.RUNNING
        ).count()
        
        if running_jobs > 0:
            raise HTTPException(
                status_code=400,
                detail="Cannot delete site with running scrape jobs"
            )
        
        db.delete(site)
        db.commit()
        
        logger.info(f"Deleted scraping site {site_id} by {current_user.username}")
        return {"message": "Scraping site deleted successfully"}

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error deleting scraping site {site_id}: {e}")
        db.rollback()
        raise HTTPException(status_code=500, detail="Failed to delete scraping site")


@router.post("/sites/test", response_model=SiteTestResponse)
async def test_scraping_site(
    test_data: SiteTestRequest,
    current_user: AdminUser = Depends(require_permission("manage_sites")),
    db: Session = Depends(get_db)
):
    """Test connectivity and basic scraping capability of a site."""
    try:
        start_time = time.time()

        # Prepare headers
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.5',
            'Accept-Encoding': 'gzip, deflate',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
        }

        # Add custom headers if provided
        if test_data.headers:
            headers.update(test_data.headers)

        # Make request with timeout
        response = requests.get(
            test_data.url,
            headers=headers,
            timeout=10,
            allow_redirects=True,
            verify=True
        )

        response_time = time.time() - start_time

        # Check response
        if response.status_code == 200:
            content_length = len(response.content)
            content_type = response.headers.get('content-type', '').lower()

            # Basic content validation
            if 'text/html' in content_type:
                message = f"Connection successful! Received {content_length} bytes of HTML content."
            else:
                message = f"Connection successful! Received {content_length} bytes of {content_type} content."

            return SiteTestResponse(
                success=True,
                message=message,
                response_time=round(response_time, 2),
                status_code=response.status_code,
                content_type=content_type,
                content_length=content_length
            )
        else:
            return SiteTestResponse(
                success=False,
                message=f"HTTP {response.status_code}: {response.reason}",
                response_time=round(response_time, 2),
                status_code=response.status_code
            )

    except requests.exceptions.Timeout:
        return SiteTestResponse(
            success=False,
            message="Connection timeout - site took too long to respond"
        )
    except requests.exceptions.ConnectionError:
        return SiteTestResponse(
            success=False,
            message="Connection failed - unable to reach the site"
        )
    except requests.exceptions.SSLError:
        return SiteTestResponse(
            success=False,
            message="SSL certificate error - site may have invalid certificate"
        )
    except requests.exceptions.RequestException as e:
        return SiteTestResponse(
            success=False,
            message=f"Request failed: {str(e)}"
        )
    except Exception as e:
        logger.error(f"Error testing site {test_data.url}: {e}")
        return SiteTestResponse(
            success=False,
            message=f"Test failed: {str(e)}"
        )


@router.post("/sites/validate", response_model=SiteTestResponse)
async def validate_scraping_rules(
    validation_data: dict,
    current_user: AdminUser = Depends(require_permission("manage_sites")),
    db: Session = Depends(get_db)
):
    """Validate scraping rules by testing them on a sample page."""
    try:
        from bs4 import BeautifulSoup

        url = validation_data.get('url')
        scraping_config = validation_data.get('scraping_config', {})

        if not url:
            return SiteTestResponse(
                success=False,
                message="URL is required for validation"
            )

        # Fetch the page
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        }

        response = requests.get(url, headers=headers, timeout=10)
        if response.status_code != 200:
            return SiteTestResponse(
                success=False,
                message=f"Failed to fetch page: HTTP {response.status_code}"
            )

        soup = BeautifulSoup(response.content, 'html.parser')
        results = {}

        # Test video selector
        video_selector = scraping_config.get('video_selector')
        if video_selector:
            video_links = soup.select(video_selector)
            results['video_links'] = {
                'count': len(video_links),
                'samples': [link.get('href', 'No href') for link in video_links[:3]]
            }

        # Test title selector
        title_selector = scraping_config.get('title_selector')
        if title_selector:
            titles = soup.select(title_selector)
            results['titles'] = {
                'count': len(titles),
                'samples': [title.get_text(strip=True) for title in titles[:3]]
            }

        # Test thumbnail selector
        thumbnail_selector = scraping_config.get('thumbnail_selector')
        if thumbnail_selector:
            thumbnails = soup.select(thumbnail_selector)
            results['thumbnails'] = {
                'count': len(thumbnails),
                'samples': [thumb.get('src', 'No src') for thumb in thumbnails[:3]]
            }

        # Generate validation message
        total_matches = sum(result.get('count', 0) for result in results.values())
        if total_matches > 0:
            message = f"Validation successful! Found {total_matches} total matches across all selectors."
        else:
            message = "No matches found. Please check your CSS selectors."

        return SiteTestResponse(
            success=total_matches > 0,
            message=message,
            validation_results=results
        )

    except ImportError:
        return SiteTestResponse(
            success=False,
            message="BeautifulSoup4 is required for scraping rule validation. Please install it."
        )
    except Exception as e:
        logger.error(f"Error validating scraping rules for {url}: {e}")
        return SiteTestResponse(
            success=False,
            message=f"Validation failed: {str(e)}"
        )


# Scraping Jobs Management
@router.get("/jobs", response_model=ScrapeJobListResponse)
async def get_scrape_jobs(
    skip: int = Query(0, ge=0),
    limit: int = Query(50, ge=1, le=100),
    site_id: Optional[int] = Query(None),
    status_filter: Optional[JobStatus] = Query(None),
    sort_by: Optional[str] = Query("created_at"),
    sort_order: Optional[str] = Query("desc"),
    current_user: AdminUser = Depends(require_permission("view_analytics")),
    db: Session = Depends(get_db)
):
    """Get paginated list of scrape jobs."""
    try:
        query = db.query(ScrapeJob)

        # Apply filters
        if site_id:
            query = query.filter(ScrapeJob.site_id == site_id)

        if status_filter:
            query = query.filter(ScrapeJob.status == status_filter)

        # Apply sorting
        sort_column = getattr(ScrapeJob, sort_by, ScrapeJob.created_at)
        if sort_order.lower() == "asc":
            query = query.order_by(asc(sort_column))
        else:
            query = query.order_by(desc(sort_column))

        # Get total count
        total = query.count()

        # Apply pagination
        jobs = query.offset(skip).limit(limit).all()

        return ScrapeJobListResponse(
            jobs=[ScrapeJobResponse.model_validate(job) for job in jobs],
            total=total,
            skip=skip,
            limit=limit,
            has_more=(skip + limit) < total
        )

    except Exception as e:
        logger.error(f"Error fetching scrape jobs: {e}")
        raise HTTPException(status_code=500, detail="Failed to fetch scrape jobs")


@router.post("/jobs", response_model=ScrapeJobResponse)
async def create_scrape_job(
    job_data: ScrapeJobCreate,
    current_user: AdminUser = Depends(require_permission("manage_scraping")),
    db: Session = Depends(get_db)
):
    """Create a new scrape job for a site."""
    try:
        # Verify site exists and is active
        site = db.query(ScrapingSite).filter(
            ScrapingSite.id == job_data.site_id,
            ScrapingSite.is_active == True
        ).first()

        if not site:
            raise HTTPException(status_code=404, detail="Active scraping site not found")

        # Check if there's already a running job for this site
        existing_job = db.query(ScrapeJob).filter(
            ScrapeJob.site_id == job_data.site_id,
            ScrapeJob.status == JobStatus.RUNNING
        ).first()

        if existing_job:
            raise HTTPException(
                status_code=400,
                detail="Site already has a running scrape job"
            )

        # Create new job
        job = ScrapeJob(
            site_id=job_data.site_id,
            status=JobStatus.PENDING,
            scrape_config_snapshot=job_data.scrape_config_override or site.scraping_config
        )

        db.add(job)
        db.commit()
        db.refresh(job)

        logger.info(f"Created scrape job {job.id} for site {site.name} by {current_user.username}")

        # Here you would typically queue the job for processing
        # For now, we'll just return the created job

        return ScrapeJobResponse.model_validate(job)

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error creating scrape job: {e}")
        db.rollback()
        raise HTTPException(status_code=500, detail="Failed to create scrape job")


@router.get("/jobs/{job_id}", response_model=ScrapeJobResponse)
async def get_scrape_job(
    job_id: int,
    current_user: AdminUser = Depends(require_permission("view_analytics")),
    db: Session = Depends(get_db)
):
    """Get a specific scrape job by ID."""
    try:
        job = db.query(ScrapeJob).filter(ScrapeJob.id == job_id).first()

        if not job:
            raise HTTPException(status_code=404, detail="Scrape job not found")

        return ScrapeJobResponse.model_validate(job)

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error fetching scrape job {job_id}: {e}")
        raise HTTPException(status_code=500, detail="Failed to fetch scrape job")


@router.post("/jobs/{job_id}/cancel")
async def cancel_scrape_job(
    job_id: int,
    current_user: AdminUser = Depends(require_permission("manage_scraping")),
    db: Session = Depends(get_db)
):
    """Cancel a running scrape job."""
    try:
        job = db.query(ScrapeJob).filter(ScrapeJob.id == job_id).first()

        if not job:
            raise HTTPException(status_code=404, detail="Scrape job not found")

        if job.status not in [JobStatus.PENDING, JobStatus.RUNNING]:
            raise HTTPException(
                status_code=400,
                detail="Can only cancel pending or running jobs"
            )

        job.status = JobStatus.CANCELLED
        job.completed_at = datetime.utcnow()
        db.commit()

        logger.info(f"Cancelled scrape job {job_id} by {current_user.username}")

        # Here you would typically signal the job processor to stop

        return {"message": "Scrape job cancelled successfully"}

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error cancelling scrape job {job_id}: {e}")
        db.rollback()
        raise HTTPException(status_code=500, detail="Failed to cancel scrape job")


# Bulk Operations
@router.post("/sites/bulk", response_model=BulkActionResponse)
async def bulk_site_action(
    action_data: BulkSiteAction,
    current_user: AdminUser = Depends(require_permission("manage_sites")),
    db: Session = Depends(get_db)
):
    """Perform bulk actions on multiple sites."""
    try:
        sites = db.query(ScrapingSite).filter(
            ScrapingSite.id.in_(action_data.site_ids)
        ).all()

        if not sites:
            raise HTTPException(status_code=404, detail="No sites found with provided IDs")

        affected_count = 0
        errors = []

        for site in sites:
            try:
                if action_data.action == "activate":
                    site.is_active = True
                    affected_count += 1
                elif action_data.action == "deactivate":
                    site.is_active = False
                    affected_count += 1
                elif action_data.action == "delete":
                    # Check for running jobs
                    running_jobs = db.query(ScrapeJob).filter(
                        ScrapeJob.site_id == site.id,
                        ScrapeJob.status == JobStatus.RUNNING
                    ).count()

                    if running_jobs > 0:
                        errors.append(f"Site {site.name} has running jobs, cannot delete")
                    else:
                        db.delete(site)
                        affected_count += 1
                elif action_data.action == "scrape":
                    # Check for existing running job
                    existing_job = db.query(ScrapeJob).filter(
                        ScrapeJob.site_id == site.id,
                        ScrapeJob.status == JobStatus.RUNNING
                    ).first()

                    if existing_job:
                        errors.append(f"Site {site.name} already has a running job")
                    else:
                        job = ScrapeJob(site_id=site.id, status=JobStatus.PENDING)
                        db.add(job)
                        affected_count += 1

            except Exception as e:
                errors.append(f"Error processing site {site.name}: {str(e)}")

        db.commit()

        success = len(errors) == 0
        message = f"Bulk {action_data.action} completed"
        if errors:
            message += f" with {len(errors)} errors"

        logger.info(f"Bulk action {action_data.action} by {current_user.username}: {affected_count} affected, {len(errors)} errors")

        return BulkActionResponse(
            success=success,
            message=message,
            affected_count=affected_count,
            errors=errors
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error in bulk site action: {e}")
        db.rollback()
        raise HTTPException(status_code=500, detail="Bulk action failed")


# Admin User Management
@router.get("/users", response_model=AdminUserListResponse)
async def get_admin_users(
    skip: int = Query(0, ge=0),
    limit: int = Query(50, ge=1, le=100),
    search: Optional[str] = Query(None),
    role_filter: Optional[UserRole] = Query(None),
    current_user: AdminUser = Depends(require_permission("manage_users")),
    db: Session = Depends(get_db)
):
    """Get paginated list of admin users."""
    try:
        query = db.query(AdminUser)

        # Apply search filter
        if search:
            query = query.filter(
                or_(
                    AdminUser.username.ilike(f"%{search}%"),
                    AdminUser.email.ilike(f"%{search}%"),
                    AdminUser.full_name.ilike(f"%{search}%")
                )
            )

        # Apply role filter
        if role_filter:
            query = query.filter(AdminUser.role == role_filter)

        # Apply sorting
        query = query.order_by(desc(AdminUser.created_at))

        # Get total count
        total = query.count()

        # Apply pagination
        users = query.offset(skip).limit(limit).all()

        return AdminUserListResponse(
            users=[AdminUserResponse.model_validate(user) for user in users],
            total=total,
            skip=skip,
            limit=limit,
            has_more=(skip + limit) < total
        )

    except Exception as e:
        logger.error(f"Error fetching admin users: {e}")
        raise HTTPException(status_code=500, detail="Failed to fetch admin users")


@router.post("/users", response_model=AdminUserResponse)
async def create_admin_user(
    user_data: AdminUserCreate,
    current_user: AdminUser = Depends(require_permission("manage_users")),
    db: Session = Depends(get_db)
):
    """Create a new admin user."""
    try:
        # Check if username or email already exists
        existing_user = db.query(AdminUser).filter(
            or_(
                AdminUser.username == user_data.username,
                AdminUser.email == user_data.email
            )
        ).first()

        if existing_user:
            raise HTTPException(
                status_code=400,
                detail="Username or email already exists"
            )

        # Only owners can create other owners
        if user_data.role == UserRole.OWNER and current_user.role != UserRole.OWNER:
            raise HTTPException(
                status_code=403,
                detail="Only owners can create other owners"
            )

        # Create new user
        user = AdminUser(
            username=user_data.username,
            email=user_data.email,
            hashed_password=hash_password(user_data.password),
            full_name=user_data.full_name,
            role=user_data.role,
            is_active=user_data.is_active
        )

        db.add(user)
        db.commit()
        db.refresh(user)

        logger.info(f"Created admin user: {user.username} by {current_user.username}")
        return AdminUserResponse.model_validate(user)

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error creating admin user: {e}")
        db.rollback()
        raise HTTPException(status_code=500, detail="Failed to create admin user")


@router.get("/users/{user_id}", response_model=AdminUserResponse)
async def get_admin_user(
    user_id: int,
    current_user: AdminUser = Depends(require_permission("manage_users")),
    db: Session = Depends(get_db)
):
    """Get a specific admin user by ID."""
    try:
        user = db.query(AdminUser).filter(AdminUser.id == user_id).first()

        if not user:
            raise HTTPException(status_code=404, detail="Admin user not found")

        return AdminUserResponse.model_validate(user)

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error fetching admin user {user_id}: {e}")
        raise HTTPException(status_code=500, detail="Failed to fetch admin user")


@router.put("/users/{user_id}", response_model=AdminUserResponse)
async def update_admin_user(
    user_id: int,
    user_data: AdminUserUpdate,
    current_user: AdminUser = Depends(require_permission("manage_users")),
    db: Session = Depends(get_db)
):
    """Update an admin user."""
    try:
        user = db.query(AdminUser).filter(AdminUser.id == user_id).first()

        if not user:
            raise HTTPException(status_code=404, detail="Admin user not found")

        # Users can only edit themselves unless they have manage_users permission
        if user_id != current_user.id and not current_user.has_permission("manage_users"):
            raise HTTPException(status_code=403, detail="Can only edit your own profile")

        # Only owners can change roles to/from owner
        if user_data.role == UserRole.OWNER or user.role == UserRole.OWNER:
            if current_user.role != UserRole.OWNER:
                raise HTTPException(
                    status_code=403,
                    detail="Only owners can manage owner roles"
                )

        # Update fields
        update_data = user_data.model_dump(exclude_unset=True)
        for field, value in update_data.items():
            if field == "password" and value:
                setattr(user, "hashed_password", hash_password(value))
            else:
                setattr(user, field, value)

        user.updated_at = datetime.utcnow()
        db.commit()
        db.refresh(user)

        logger.info(f"Updated admin user {user_id} by {current_user.username}")
        return AdminUserResponse.model_validate(user)

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error updating admin user {user_id}: {e}")
        db.rollback()
        raise HTTPException(status_code=500, detail="Failed to update admin user")


@router.delete("/users/{user_id}")
async def delete_admin_user(
    user_id: int,
    current_user: AdminUser = Depends(require_permission("manage_users")),
    db: Session = Depends(get_db)
):
    """Delete an admin user."""
    try:
        user = db.query(AdminUser).filter(AdminUser.id == user_id).first()

        if not user:
            raise HTTPException(status_code=404, detail="Admin user not found")

        # Cannot delete yourself
        if user_id == current_user.id:
            raise HTTPException(status_code=400, detail="Cannot delete your own account")

        # Only owners can delete other owners
        if user.role == UserRole.OWNER and current_user.role != UserRole.OWNER:
            raise HTTPException(
                status_code=403,
                detail="Only owners can delete other owners"
            )

        db.delete(user)
        db.commit()

        logger.info(f"Deleted admin user {user_id} by {current_user.username}")
        return {"message": "Admin user deleted successfully"}

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error deleting admin user {user_id}: {e}")
        db.rollback()
        raise HTTPException(status_code=500, detail="Failed to delete admin user")
