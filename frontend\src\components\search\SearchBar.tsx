import React, { useState, useRef, useEffect } from 'react'
import { Search, X, Clock, Filter, Zap } from 'lucide-react'
import { useQuery } from '@tanstack/react-query'
import { Button } from '@/components/ui/Button'
import { Badge } from '@/components/ui/Badge'
import { useAppStore } from '@/store'
import { getSearchSuggestions } from '@/services/api'
import { cn } from '@/utils/cn'
import type { SearchSuggestion } from '@/types'

interface SearchBarProps {
  onSearch: (query: string) => void
  onSuggestionSelect?: (suggestion: SearchSuggestion) => void
  placeholder?: string
  className?: string
  autoFocus?: boolean
  showQuickFilters?: boolean
  initialValue?: string
}

export function SearchBar({
  onSearch,
  onSuggestionSelect,
  placeholder = "Search...",
  className,
  autoFocus = false,
  showQuickFilters = false,
  initialValue = '',
}: SearchBarProps) {
  const [query, setQuery] = useState(initialValue)
  const [showSuggestions, setShowSuggestions] = useState(false)
  const [selectedIndex, setSelectedIndex] = useState(-1)
  const inputRef = useRef<HTMLInputElement>(null)
  const suggestionsRef = useRef<HTMLDivElement>(null)
  
  const { addRecentSearch, recentSearches } = useAppStore()

  // Fetch suggestions
  const { data: suggestionsData } = useQuery({
    queryKey: ['search-suggestions', query],
    queryFn: () => getSearchSuggestions(query),
    enabled: query.length >= 2,
    staleTime: 5 * 60 * 1000, // 5 minutes
  })

  const suggestions = suggestionsData?.suggestions || []
  const allSuggestions = [
    ...suggestions,
    ...recentSearches
      .filter(search => search.toLowerCase().includes(query.toLowerCase()))
      .map(search => ({ text: search, type: 'recent' as const }))
  ].slice(0, 8)

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    if (query.trim()) {
      onSearch(query.trim())
      addRecentSearch(query.trim())
      setShowSuggestions(false)
      setQuery('')
      inputRef.current?.blur()
    }
  }

  const handleSuggestionClick = (suggestion: SearchSuggestion | { text: string; type: string }) => {
    const searchQuery = suggestion.text
    setQuery(searchQuery)
    onSearch(searchQuery)
    addRecentSearch(searchQuery)
    setShowSuggestions(false)
    
    if (onSuggestionSelect && 'type' in suggestion) {
      onSuggestionSelect(suggestion as SearchSuggestion)
    }
  }

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (!showSuggestions || allSuggestions.length === 0) return

    switch (e.key) {
      case 'ArrowDown':
        e.preventDefault()
        setSelectedIndex(prev => 
          prev < allSuggestions.length - 1 ? prev + 1 : 0
        )
        break
      case 'ArrowUp':
        e.preventDefault()
        setSelectedIndex(prev => 
          prev > 0 ? prev - 1 : allSuggestions.length - 1
        )
        break
      case 'Enter':
        if (selectedIndex >= 0) {
          e.preventDefault()
          handleSuggestionClick(allSuggestions[selectedIndex])
        }
        break
      case 'Escape':
        setShowSuggestions(false)
        setSelectedIndex(-1)
        break
    }
  }

  const clearQuery = () => {
    setQuery('')
    setShowSuggestions(false)
    inputRef.current?.focus()
  }

  useEffect(() => {
    if (autoFocus && inputRef.current) {
      inputRef.current.focus()
    }
  }, [autoFocus])

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        suggestionsRef.current &&
        !suggestionsRef.current.contains(event.target as Node) &&
        !inputRef.current?.contains(event.target as Node)
      ) {
        setShowSuggestions(false)
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => document.removeEventListener('mousedown', handleClickOutside)
  }, [])

  const getSuggestionIcon = (type: string) => {
    switch (type) {
      case 'video_title':
        return '🎬'
      case 'category':
        return '📁'
      case 'performer':
        return '👤'
      case 'recent':
        return '🕒'
      default:
        return '🔍'
    }
  }

  const quickFilters = [
    { label: 'HD', query: 'quality:hd' },
    { label: '4K', query: 'quality:4k' },
    { label: 'Recent', query: 'sort:recent' },
    { label: 'Popular', query: 'sort:popular' },
    { label: 'Top Rated', query: 'sort:rating' },
  ]

  const handleQuickFilter = (filterQuery: string) => {
    setQuery(filterQuery)
    onSearch(filterQuery)
    addRecentSearch(filterQuery)
    setShowSuggestions(false)
  }

  return (
    <div className={cn("relative", className)}>
      <form onSubmit={handleSubmit} className="relative">
        <div className="relative">
          <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
          <input
            ref={inputRef}
            type="text"
            value={query}
            onChange={(e) => {
              setQuery(e.target.value)
              setShowSuggestions(true)
              setSelectedIndex(-1)
            }}
            onFocus={() => setShowSuggestions(true)}
            onKeyDown={handleKeyDown}
            placeholder={placeholder}
            className="input pl-10 pr-10"
          />
          {query && (
            <Button
              type="button"
              variant="ghost"
              size="sm"
              onClick={clearQuery}
              className="absolute right-1 top-1/2 h-8 w-8 -translate-y-1/2 p-0"
            >
              <X className="h-4 w-4" />
            </Button>
          )}
        </div>
      </form>

      {/* Suggestions dropdown */}
      {showSuggestions && allSuggestions.length > 0 && (
        <div
          ref={suggestionsRef}
          className="absolute top-full left-0 right-0 z-50 mt-1 max-h-80 overflow-auto rounded-md border bg-popover p-1 shadow-md"
        >
          {allSuggestions.map((suggestion, index) => (
            <button
              key={`${suggestion.type}-${suggestion.text}-${index}`}
              onClick={() => handleSuggestionClick(suggestion)}
              className={cn(
                "flex w-full items-center gap-3 rounded-sm px-3 py-2 text-left text-sm hover:bg-accent hover:text-accent-foreground",
                index === selectedIndex && "bg-accent text-accent-foreground"
              )}
            >
              <span className="text-base">
                {getSuggestionIcon(suggestion.type)}
              </span>
              <span className="flex-1 truncate">{suggestion.text}</span>
              <span className="text-xs text-muted-foreground capitalize">
                {suggestion.type === 'recent' ? 'recent' : suggestion.type.replace('_', ' ')}
              </span>
            </button>
          ))}
        </div>
      )}

      {/* Quick Filters */}
      {showQuickFilters && !showSuggestions && (
        <div className="flex flex-wrap gap-2 mt-3">
          <span className="text-sm text-muted-foreground flex items-center gap-1">
            <Zap className="h-3 w-3" />
            Quick filters:
          </span>
          {quickFilters.map((filter) => (
            <Button
              key={filter.label}
              variant="outline"
              size="sm"
              onClick={() => handleQuickFilter(filter.query)}
              className="text-xs h-7"
            >
              {filter.label}
            </Button>
          ))}
        </div>
      )}
    </div>
  )
}
