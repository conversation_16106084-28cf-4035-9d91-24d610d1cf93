/**
 * Site configuration templates for popular video platforms
 */

export interface SiteTemplate {
  id: string;
  name: string;
  description: string;
  category: 'adult' | 'general' | 'streaming' | 'social';
  icon: string;
  config: {
    site_type: 'video' | 'image' | 'mixed';
    content_rating: 'adult' | 'general' | 'mixed';
    language: string;
    priority: number;
    scraping_interval_hours: number;
    max_pages_per_scrape: number;
    rate_limit_delay: number;
    requires_auth: boolean;
    scraping_config: {
      video_selector?: string;
      title_selector?: string;
      thumbnail_selector?: string;
      duration_selector?: string;
      view_count_selector?: string;
      rating_selector?: string;
      description_selector?: string;
      tags_selector?: string;
      performer_selector?: string;
      url_patterns?: string[];
      exclude_patterns?: string[];
      min_duration?: number;
      min_resolution?: number;
      concurrent_requests?: number;
      timeout?: number;
    };
    headers: Record<string, string>;
  };
}

export const siteTemplates: SiteTemplate[] = [
  {
    id: 'generic-video',
    name: 'Generic Video Site',
    description: 'Basic template for most video sharing sites',
    category: 'general',
    icon: '🎥',
    config: {
      site_type: 'video',
      content_rating: 'general',
      language: 'en',
      priority: 5,
      scraping_interval_hours: 24,
      max_pages_per_scrape: 10,
      rate_limit_delay: 1.0,
      requires_auth: false,
      scraping_config: {
        video_selector: 'a[href*="/video/"], a[href*="/watch/"]',
        title_selector: 'h1, .title, .video-title',
        thumbnail_selector: 'img.thumbnail, .preview img, .video-thumb',
        duration_selector: '.duration, .time',
        view_count_selector: '.views, .view-count',
        url_patterns: ['/video/*', '/watch/*'],
        exclude_patterns: ['/ads/*', '/popup/*', '/login/*'],
        min_duration: 30,
        concurrent_requests: 1,
        timeout: 30
      },
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
        'Accept-Language': 'en-US,en;q=0.5'
      }
    }
  },
  {
    id: 'adult-tube',
    name: 'Adult Tube Site',
    description: 'Template for adult video tube sites',
    category: 'adult',
    icon: '🔞',
    config: {
      site_type: 'video',
      content_rating: 'adult',
      language: 'en',
      priority: 5,
      scraping_interval_hours: 12,
      max_pages_per_scrape: 20,
      rate_limit_delay: 2.0,
      requires_auth: false,
      scraping_config: {
        video_selector: 'a[href*="/video/"], a[href*="/watch/"], .video-link',
        title_selector: 'h1.video-title, .title, h2.title',
        thumbnail_selector: 'img.thumb, .thumbnail img, .video-thumb',
        duration_selector: '.duration, .time, .video-duration',
        view_count_selector: '.views, .view-count, .stats .views',
        rating_selector: '.rating, .vote-score, .likes',
        performer_selector: '.pornstar, .model, .performer a',
        tags_selector: '.tags a, .categories a, .tag',
        url_patterns: ['/video/*', '/watch/*', '/scene/*'],
        exclude_patterns: ['/ads/*', '/popup/*', '/join/*', '/signup/*'],
        min_duration: 60,
        min_resolution: 480,
        concurrent_requests: 1,
        timeout: 30
      },
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
        'Accept-Language': 'en-US,en;q=0.5',
        'Referer': 'https://www.google.com/'
      }
    }
  },
  {
    id: 'streaming-platform',
    name: 'Streaming Platform',
    description: 'Template for streaming video platforms',
    category: 'streaming',
    icon: '📺',
    config: {
      site_type: 'video',
      content_rating: 'general',
      language: 'en',
      priority: 7,
      scraping_interval_hours: 6,
      max_pages_per_scrape: 5,
      rate_limit_delay: 3.0,
      requires_auth: true,
      scraping_config: {
        video_selector: 'a[href*="/video/"], a[href*="/watch/"], .content-link',
        title_selector: 'h1, .video-title, .content-title',
        thumbnail_selector: '.thumbnail, .poster, .preview-image',
        duration_selector: '.duration, .runtime',
        description_selector: '.description, .synopsis, .summary',
        url_patterns: ['/video/*', '/watch/*', '/content/*'],
        exclude_patterns: ['/ads/*', '/promo/*'],
        min_duration: 300,
        min_resolution: 720,
        concurrent_requests: 1,
        timeout: 60
      },
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
        'Accept-Language': 'en-US,en;q=0.5'
      }
    }
  },
  {
    id: 'social-video',
    name: 'Social Video Platform',
    description: 'Template for social media video platforms',
    category: 'social',
    icon: '📱',
    config: {
      site_type: 'video',
      content_rating: 'mixed',
      language: 'en',
      priority: 6,
      scraping_interval_hours: 2,
      max_pages_per_scrape: 50,
      rate_limit_delay: 1.5,
      requires_auth: false,
      scraping_config: {
        video_selector: 'a[href*="/video/"], .video-item a, .post-video a',
        title_selector: '.video-title, .post-title, .caption',
        thumbnail_selector: '.video-thumbnail, .post-image, video',
        view_count_selector: '.view-count, .stats .views, .engagement .views',
        tags_selector: '.hashtags a, .tags a',
        url_patterns: ['/video/*', '/post/*', '/v/*'],
        exclude_patterns: ['/ads/*', '/sponsored/*'],
        min_duration: 5,
        concurrent_requests: 2,
        timeout: 20
      },
      headers: {
        'User-Agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 14_7_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.2 Mobile/15E148 Safari/604.1',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
        'Accept-Language': 'en-US,en;q=0.5'
      }
    }
  },
  {
    id: 'image-gallery',
    name: 'Image Gallery Site',
    description: 'Template for image gallery and photo sharing sites',
    category: 'general',
    icon: '🖼️',
    config: {
      site_type: 'image',
      content_rating: 'general',
      language: 'en',
      priority: 4,
      scraping_interval_hours: 24,
      max_pages_per_scrape: 15,
      rate_limit_delay: 1.0,
      requires_auth: false,
      scraping_config: {
        video_selector: 'a[href*="/image/"], a[href*="/photo/"], .image-link',
        title_selector: '.image-title, .photo-title, h1',
        thumbnail_selector: '.thumbnail, .preview, img.thumb',
        tags_selector: '.tags a, .keywords a',
        url_patterns: ['/image/*', '/photo/*', '/gallery/*'],
        exclude_patterns: ['/ads/*', '/popup/*'],
        concurrent_requests: 2,
        timeout: 30
      },
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
        'Accept-Language': 'en-US,en;q=0.5'
      }
    }
  },
  {
    id: 'mixed-content',
    name: 'Mixed Content Site',
    description: 'Template for sites with both videos and images',
    category: 'general',
    icon: '🎭',
    config: {
      site_type: 'mixed',
      content_rating: 'mixed',
      language: 'en',
      priority: 5,
      scraping_interval_hours: 18,
      max_pages_per_scrape: 12,
      rate_limit_delay: 1.5,
      requires_auth: false,
      scraping_config: {
        video_selector: 'a[href*="/video/"], a[href*="/image/"], .content-link',
        title_selector: '.content-title, .media-title, h1, h2.title',
        thumbnail_selector: '.thumbnail, .preview, .media-thumb',
        duration_selector: '.duration',
        tags_selector: '.tags a, .categories a',
        url_patterns: ['/video/*', '/image/*', '/content/*'],
        exclude_patterns: ['/ads/*', '/popup/*', '/premium/*'],
        min_duration: 10,
        concurrent_requests: 1,
        timeout: 30
      },
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
        'Accept-Language': 'en-US,en;q=0.5'
      }
    }
  }
];

export const getTemplatesByCategory = (category?: string) => {
  if (!category) return siteTemplates;
  return siteTemplates.filter(template => template.category === category);
};

export const getTemplateById = (id: string) => {
  return siteTemplates.find(template => template.id === id);
};
