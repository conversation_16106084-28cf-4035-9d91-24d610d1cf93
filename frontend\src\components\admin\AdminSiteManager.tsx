/**
 * Admin site manager component for managing scraping sites
 */

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../ui/Card';
import { Button } from '../ui/Button';
import { Input } from '../ui/Input';
import { Badge } from '../ui/Badge';
import { 
  Plus, 
  Search, 
  Globe, 
  Play, 
  Pause, 
  Trash2, 
  Edit, 
  RefreshCw,
  CheckCircle,
  XCircle,
  AlertTriangle,
  Clock
} from 'lucide-react';
import { adminApi, ScrapingSite } from '../../services/adminApi';
import { useAdminAuth } from '../../contexts/AdminAuthContext';

interface AdminSiteManagerProps {
  onStatsUpdate?: () => void;
}

export const AdminSiteManager: React.FC<AdminSiteManagerProps> = ({ onStatsUpdate }) => {
  const { hasPermission } = useAdminAuth();
  const [sites, setSites] = useState<ScrapingSite[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState('');
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [selectedSites, setSelectedSites] = useState<number[]>([]);

  const loadSites = async () => {
    try {
      setIsLoading(true);
      setError('');
      const response = await adminApi.getSites({
        search: searchTerm || undefined,
        status_filter: statusFilter === 'all' ? undefined : statusFilter,
        limit: 100,
      });
      setSites(response.sites);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load sites');
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    loadSites();
  }, [searchTerm, statusFilter]);

  const handleBulkAction = async (action: 'activate' | 'deactivate' | 'delete' | 'scrape') => {
    if (selectedSites.length === 0) return;

    try {
      setIsLoading(true);
      await adminApi.bulkSiteAction(action, selectedSites);
      setSelectedSites([]);
      await loadSites();
      onStatsUpdate?.();
    } catch (err) {
      setError(err instanceof Error ? err.message : `Failed to ${action} sites`);
    } finally {
      setIsLoading(false);
    }
  };

  const handleToggleSite = async (site: ScrapingSite) => {
    try {
      await adminApi.updateSite(site.id, { is_active: !site.is_active });
      await loadSites();
      onStatsUpdate?.();
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to update site');
    }
  };

  const handleStartScraping = async (siteId: number) => {
    try {
      await adminApi.createJob(siteId);
      onStatsUpdate?.();
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to start scraping job');
    }
  };

  const getStatusIcon = (site: ScrapingSite) => {
    switch (site.status) {
      case 'active':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'inactive':
        return <Pause className="h-4 w-4 text-gray-500" />;
      case 'failing':
        return <AlertTriangle className="h-4 w-4 text-orange-500" />;
      case 'never_scraped':
        return <Clock className="h-4 w-4 text-blue-500" />;
      default:
        return <XCircle className="h-4 w-4 text-red-500" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300';
      case 'inactive': return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300';
      case 'failing': return 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-300';
      case 'never_scraped': return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300';
      default: return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300';
    }
  };

  const formatNumber = (num: number) => {
    return new Intl.NumberFormat().format(num);
  };

  const formatDate = (dateString?: string) => {
    if (!dateString) return 'Never';
    return new Date(dateString).toLocaleDateString();
  };

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle>Scraping Sites</CardTitle>
            <CardDescription>Manage video scraping sources</CardDescription>
          </div>
          {hasPermission('manage_sites') && (
            <Button>
              <Plus className="h-4 w-4 mr-2" />
              Add Site
            </Button>
          )}
        </div>
      </CardHeader>
      <CardContent>
        {/* Filters and Search */}
        <div className="flex flex-col sm:flex-row gap-4 mb-6">
          <div className="flex-1">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search sites..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
          </div>
          <div className="flex gap-2">
            <select
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value)}
              className="px-3 py-2 border rounded-md bg-background"
            >
              <option value="all">All Status</option>
              <option value="active">Active</option>
              <option value="inactive">Inactive</option>
              <option value="failing">Failing</option>
              <option value="never_scraped">Never Scraped</option>
            </select>
            <Button variant="outline" onClick={loadSites} disabled={isLoading}>
              <RefreshCw className={`h-4 w-4 ${isLoading ? 'animate-spin' : ''}`} />
            </Button>
          </div>
        </div>

        {/* Bulk Actions */}
        {selectedSites.length > 0 && hasPermission('manage_sites') && (
          <div className="flex gap-2 mb-4 p-3 bg-muted rounded-lg">
            <span className="text-sm font-medium">{selectedSites.length} selected:</span>
            <Button size="sm" variant="outline" onClick={() => handleBulkAction('activate')}>
              Activate
            </Button>
            <Button size="sm" variant="outline" onClick={() => handleBulkAction('deactivate')}>
              Deactivate
            </Button>
            <Button size="sm" variant="outline" onClick={() => handleBulkAction('scrape')}>
              Start Scraping
            </Button>
            <Button size="sm" variant="destructive" onClick={() => handleBulkAction('delete')}>
              Delete
            </Button>
          </div>
        )}

        {error && (
          <div className="mb-4 p-3 bg-destructive/10 border border-destructive/20 rounded-lg text-destructive">
            {error}
          </div>
        )}

        {/* Sites List */}
        <div className="space-y-4">
          {sites.map((site) => (
            <div key={site.id} className="border rounded-lg p-4">
              <div className="flex items-start justify-between">
                <div className="flex items-start gap-3 flex-1">
                  {hasPermission('manage_sites') && (
                    <input
                      type="checkbox"
                      checked={selectedSites.includes(site.id)}
                      onChange={(e) => {
                        if (e.target.checked) {
                          setSelectedSites([...selectedSites, site.id]);
                        } else {
                          setSelectedSites(selectedSites.filter(id => id !== site.id));
                        }
                      }}
                      className="mt-1"
                    />
                  )}
                  <div className="flex-1">
                    <div className="flex items-center gap-2 mb-2">
                      {getStatusIcon(site)}
                      <h3 className="font-semibold">{site.name}</h3>
                      <Badge className={getStatusColor(site.status)}>
                        {site.status.replace('_', ' ')}
                      </Badge>
                      <Badge variant="outline">Priority {site.priority}</Badge>
                    </div>
                    <p className="text-sm text-muted-foreground mb-2">{site.description}</p>
                    <div className="flex items-center gap-4 text-sm text-muted-foreground">
                      <span className="flex items-center gap-1">
                        <Globe className="h-3 w-3" />
                        {site.base_url}
                      </span>
                      <span>{formatNumber(site.total_videos_scraped)} videos</span>
                      <span>{site.success_rate.toFixed(1)}% success</span>
                      <span>Last: {formatDate(site.last_scraped_at)}</span>
                    </div>
                  </div>
                </div>
                {hasPermission('manage_sites') && (
                  <div className="flex items-center gap-2">
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => handleToggleSite(site)}
                    >
                      {site.is_active ? <Pause className="h-4 w-4" /> : <Play className="h-4 w-4" />}
                    </Button>
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => handleStartScraping(site.id)}
                      disabled={!site.is_active}
                    >
                      <RefreshCw className="h-4 w-4" />
                    </Button>
                    <Button size="sm" variant="outline">
                      <Edit className="h-4 w-4" />
                    </Button>
                    <Button size="sm" variant="destructive">
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                )}
              </div>
            </div>
          ))}
          {sites.length === 0 && !isLoading && (
            <div className="text-center py-8 text-muted-foreground">
              No sites found. {hasPermission('manage_sites') && 'Add your first scraping site to get started.'}
            </div>
          )}
          {isLoading && (
            <div className="text-center py-8">
              <RefreshCw className="h-6 w-6 animate-spin mx-auto mb-2" />
              <p className="text-muted-foreground">Loading sites...</p>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
};
