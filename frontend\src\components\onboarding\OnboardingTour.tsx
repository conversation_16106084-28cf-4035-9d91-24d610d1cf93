import React, { useState, useEffect, useRef } from 'react'
import { ChevronLeft, ChevronRight, X, Play, Lightbulb, Target } from 'lucide-react'
import { Button } from '@/components/ui/Button'
import { Badge } from '@/components/ui/Badge'
import { cn } from '@/utils/cn'

interface TourStep {
  id: string
  title: string
  content: string
  target: string // CSS selector
  position: 'top' | 'bottom' | 'left' | 'right'
  action?: () => void
  optional?: boolean
}

interface OnboardingTourProps {
  steps: TourStep[]
  onComplete: () => void
  onSkip: () => void
  className?: string
}

export function OnboardingTour({ steps, onComplete, onSkip, className }: OnboardingTourProps) {
  const [currentStep, setCurrentStep] = useState(0)
  const [isVisible, setIsVisible] = useState(true)
  const [targetElement, setTargetElement] = useState<HTMLElement | null>(null)
  const [tooltipPosition, setTooltipPosition] = useState({ x: 0, y: 0 })
  const tooltipRef = useRef<HTMLDivElement>(null)

  const currentStepData = steps[currentStep]

  useEffect(() => {
    if (!currentStepData || !isVisible) return

    const element = document.querySelector(currentStepData.target) as HTMLElement
    if (element) {
      setTargetElement(element)
      
      // Scroll element into view
      element.scrollIntoView({ behavior: 'smooth', block: 'center' })
      
      // Highlight element
      element.classList.add('tour-highlight')
      
      // Calculate tooltip position
      updateTooltipPosition(element)
    }

    return () => {
      if (element) {
        element.classList.remove('tour-highlight')
      }
    }
  }, [currentStep, currentStepData, isVisible])

  const updateTooltipPosition = (element: HTMLElement) => {
    if (!tooltipRef.current) return

    const elementRect = element.getBoundingClientRect()
    const tooltipRect = tooltipRef.current.getBoundingClientRect()
    const viewport = {
      width: window.innerWidth,
      height: window.innerHeight,
    }

    let x = 0
    let y = 0

    switch (currentStepData.position) {
      case 'top':
        x = elementRect.left + elementRect.width / 2 - tooltipRect.width / 2
        y = elementRect.top - tooltipRect.height - 16
        break
      case 'bottom':
        x = elementRect.left + elementRect.width / 2 - tooltipRect.width / 2
        y = elementRect.bottom + 16
        break
      case 'left':
        x = elementRect.left - tooltipRect.width - 16
        y = elementRect.top + elementRect.height / 2 - tooltipRect.height / 2
        break
      case 'right':
        x = elementRect.right + 16
        y = elementRect.top + elementRect.height / 2 - tooltipRect.height / 2
        break
    }

    // Keep tooltip within viewport
    x = Math.max(16, Math.min(x, viewport.width - tooltipRect.width - 16))
    y = Math.max(16, Math.min(y, viewport.height - tooltipRect.height - 16))

    setTooltipPosition({ x, y })
  }

  const nextStep = () => {
    if (currentStepData.action) {
      currentStepData.action()
    }

    if (currentStep < steps.length - 1) {
      setCurrentStep(currentStep + 1)
    } else {
      completeTour()
    }
  }

  const prevStep = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1)
    }
  }

  const skipTour = () => {
    setIsVisible(false)
    onSkip()
  }

  const completeTour = () => {
    setIsVisible(false)
    onComplete()
  }

  const goToStep = (stepIndex: number) => {
    setCurrentStep(stepIndex)
  }

  if (!isVisible || !currentStepData) return null

  return (
    <>
      {/* Overlay */}
      <div className="fixed inset-0 bg-black/50 z-40 pointer-events-none" />
      
      {/* Spotlight */}
      {targetElement && (
        <div
          className="fixed border-4 border-primary rounded-lg pointer-events-none z-50 transition-all duration-300"
          style={{
            left: targetElement.getBoundingClientRect().left - 4,
            top: targetElement.getBoundingClientRect().top - 4,
            width: targetElement.getBoundingClientRect().width + 8,
            height: targetElement.getBoundingClientRect().height + 8,
          }}
        />
      )}

      {/* Tooltip */}
      <div
        ref={tooltipRef}
        className={cn(
          "fixed z-50 bg-card border rounded-lg shadow-xl max-w-sm p-6 animate-in fade-in-0 zoom-in-95",
          className
        )}
        style={{
          left: tooltipPosition.x,
          top: tooltipPosition.y,
        }}
      >
        {/* Header */}
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center gap-2">
            <div className="p-1 bg-primary/10 rounded">
              <Target className="h-4 w-4 text-primary" />
            </div>
            <Badge variant="outline" className="text-xs">
              {currentStep + 1} of {steps.length}
            </Badge>
          </div>
          <Button variant="ghost" size="sm" onClick={skipTour}>
            <X className="h-4 w-4" />
          </Button>
        </div>

        {/* Content */}
        <div className="space-y-3">
          <h3 className="font-semibold text-lg">{currentStepData.title}</h3>
          <p className="text-sm text-muted-foreground leading-relaxed">
            {currentStepData.content}
          </p>
        </div>

        {/* Progress */}
        <div className="mt-4 mb-4">
          <div className="flex gap-1">
            {steps.map((_, index) => (
              <button
                key={index}
                onClick={() => goToStep(index)}
                className={cn(
                  "h-2 rounded-full transition-all",
                  index === currentStep
                    ? "bg-primary flex-1"
                    : index < currentStep
                    ? "bg-primary/60 w-2"
                    : "bg-muted w-2"
                )}
              />
            ))}
          </div>
        </div>

        {/* Actions */}
        <div className="flex items-center justify-between">
          <Button
            variant="ghost"
            size="sm"
            onClick={prevStep}
            disabled={currentStep === 0}
          >
            <ChevronLeft className="h-4 w-4 mr-1" />
            Back
          </Button>

          <div className="flex gap-2">
            <Button variant="outline" size="sm" onClick={skipTour}>
              Skip Tour
            </Button>
            <Button size="sm" onClick={nextStep}>
              {currentStep === steps.length - 1 ? 'Finish' : 'Next'}
              {currentStep < steps.length - 1 && (
                <ChevronRight className="h-4 w-4 ml-1" />
              )}
            </Button>
          </div>
        </div>
      </div>

      {/* Arrow pointing to target */}
      {targetElement && (
        <div
          className="fixed z-50 pointer-events-none"
          style={{
            left: tooltipPosition.x + (currentStepData.position === 'left' ? -8 : currentStepData.position === 'right' ? 8 : 0),
            top: tooltipPosition.y + (currentStepData.position === 'top' ? -8 : currentStepData.position === 'bottom' ? 8 : 0),
          }}
        >
          <div
            className={cn(
              "w-4 h-4 bg-card border-2 border-border rotate-45",
              {
                'transform translate-x-1/2': currentStepData.position === 'top' || currentStepData.position === 'bottom',
                'transform translate-y-1/2': currentStepData.position === 'left' || currentStepData.position === 'right',
              }
            )}
          />
        </div>
      )}
    </>
  )
}

// Predefined tour configurations
export const homeTourSteps: TourStep[] = [
  {
    id: 'welcome',
    title: 'Welcome to the Video Platform!',
    content: 'Let\'s take a quick tour to help you get started with discovering amazing videos.',
    target: 'body',
    position: 'bottom',
  },
  {
    id: 'search',
    title: 'Search for Videos',
    content: 'Use the search bar to find specific videos, categories, or performers. Try typing something!',
    target: 'input[type="search"], input[placeholder*="search" i]',
    position: 'bottom',
  },
  {
    id: 'categories',
    title: 'Browse Categories',
    content: 'Explore different video categories to discover content that interests you.',
    target: '[data-tour="categories"]',
    position: 'top',
  },
  {
    id: 'favorites',
    title: 'Save Your Favorites',
    content: 'Click the heart icon on any video to save it to your favorites for easy access later.',
    target: '[data-tour="favorites"]',
    position: 'bottom',
  },
  {
    id: 'theme',
    title: 'Customize Your Experience',
    content: 'Toggle between light and dark themes, or let the system decide based on your preferences.',
    target: '[data-tour="theme-toggle"]',
    position: 'bottom',
  },
]

export const searchTourSteps: TourStep[] = [
  {
    id: 'search-input',
    title: 'Enter Your Search',
    content: 'Type keywords to search for videos. You\'ll see suggestions as you type.',
    target: 'input[type="search"]',
    position: 'bottom',
  },
  {
    id: 'filters',
    title: 'Use Filters',
    content: 'Click the Filters button to narrow down your search by category, duration, quality, and more.',
    target: '[data-tour="filters-button"]',
    position: 'bottom',
  },
  {
    id: 'view-modes',
    title: 'Switch View Modes',
    content: 'Toggle between grid and list views to see results in your preferred layout.',
    target: '[data-tour="view-toggle"]',
    position: 'bottom',
  },
  {
    id: 'save-search',
    title: 'Save Your Searches',
    content: 'Save frequently used searches and filters for quick access later.',
    target: '[data-tour="save-search"]',
    position: 'left',
  },
]

// Hook for managing onboarding
export function useOnboarding() {
  const [hasCompletedOnboarding, setHasCompletedOnboarding] = useState(false)
  const [currentTour, setCurrentTour] = useState<string | null>(null)

  useEffect(() => {
    const completed = localStorage.getItem('onboarding-completed')
    setHasCompletedOnboarding(completed === 'true')
  }, [])

  const startTour = (tourId: string) => {
    setCurrentTour(tourId)
  }

  const completeTour = () => {
    setCurrentTour(null)
    if (!hasCompletedOnboarding) {
      localStorage.setItem('onboarding-completed', 'true')
      setHasCompletedOnboarding(true)
    }
  }

  const skipTour = () => {
    setCurrentTour(null)
  }

  const resetOnboarding = () => {
    localStorage.removeItem('onboarding-completed')
    setHasCompletedOnboarding(false)
  }

  return {
    hasCompletedOnboarding,
    currentTour,
    startTour,
    completeTour,
    skipTour,
    resetOnboarding,
  }
}
