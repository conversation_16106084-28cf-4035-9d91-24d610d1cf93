/**
 * Admin job monitor component for tracking scraping jobs
 */

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../ui/Card';
import { Button } from '../ui/Button';
import { Badge } from '../ui/Badge';
import { 
  Play, 
  Pause, 
  CheckCircle, 
  XCircle, 
  Clock, 
  RefreshCw,
  Activity,
  Database,
  AlertTriangle,
  StopCircle
} from 'lucide-react';
import { adminApi, ScrapeJob } from '../../services/adminApi';
import { useAdminAuth } from '../../contexts/AdminAuthContext';

interface AdminJobMonitorProps {
  onStatsUpdate?: () => void;
}

export const AdminJobMonitor: React.FC<AdminJobMonitorProps> = ({ onStatsUpdate }) => {
  const { hasPermission } = useAdminAuth();
  const [jobs, setJobs] = useState<ScrapeJob[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');

  const loadJobs = async () => {
    try {
      setIsLoading(true);
      setError('');
      const response = await adminApi.getJobs({
        status_filter: statusFilter === 'all' ? undefined : statusFilter,
        limit: 50,
        sort_by: 'created_at',
        sort_order: 'desc',
      });
      setJobs(response.jobs);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load jobs');
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    loadJobs();
    
    // Auto-refresh every 30 seconds for running jobs
    const interval = setInterval(() => {
      if (jobs.some(job => job.status === 'running' || job.status === 'pending')) {
        loadJobs();
      }
    }, 30000);

    return () => clearInterval(interval);
  }, [statusFilter]);

  const handleCancelJob = async (jobId: number) => {
    if (!hasPermission('manage_scraping')) return;

    try {
      await adminApi.cancelJob(jobId);
      await loadJobs();
      onStatsUpdate?.();
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to cancel job');
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'pending':
        return <Clock className="h-4 w-4 text-blue-500" />;
      case 'running':
        return <Activity className="h-4 w-4 text-orange-500 animate-pulse" />;
      case 'completed':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'failed':
        return <XCircle className="h-4 w-4 text-red-500" />;
      case 'cancelled':
        return <StopCircle className="h-4 w-4 text-gray-500" />;
      default:
        return <AlertTriangle className="h-4 w-4 text-yellow-500" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending': return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300';
      case 'running': return 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-300';
      case 'completed': return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300';
      case 'failed': return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300';
      case 'cancelled': return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300';
      default: return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300';
    }
  };

  const formatNumber = (num: number) => {
    return new Intl.NumberFormat().format(num);
  };

  const formatDuration = (seconds?: number) => {
    if (!seconds) return 'N/A';
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = Math.floor(seconds % 60);
    return `${minutes}m ${remainingSeconds}s`;
  };

  const formatDate = (dateString?: string) => {
    if (!dateString) return 'N/A';
    return new Date(dateString).toLocaleString();
  };

  const getProgressPercentage = (job: ScrapeJob) => {
    if (job.status === 'completed') return 100;
    if (job.status === 'failed' || job.status === 'cancelled') return 0;
    if (job.status === 'running' && job.pages_scraped > 0) {
      // Estimate progress based on pages scraped (assuming max 10 pages)
      return Math.min((job.pages_scraped / 10) * 100, 90);
    }
    return job.status === 'running' ? 10 : 0;
  };

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle>Scraping Jobs</CardTitle>
            <CardDescription>Monitor and manage scraping job activity</CardDescription>
          </div>
          <div className="flex gap-2">
            <select
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value)}
              className="px-3 py-2 border rounded-md bg-background"
            >
              <option value="all">All Status</option>
              <option value="pending">Pending</option>
              <option value="running">Running</option>
              <option value="completed">Completed</option>
              <option value="failed">Failed</option>
              <option value="cancelled">Cancelled</option>
            </select>
            <Button variant="outline" onClick={loadJobs} disabled={isLoading}>
              <RefreshCw className={`h-4 w-4 ${isLoading ? 'animate-spin' : ''}`} />
            </Button>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        {error && (
          <div className="mb-4 p-3 bg-destructive/10 border border-destructive/20 rounded-lg text-destructive">
            {error}
          </div>
        )}

        <div className="space-y-4">
          {jobs.map((job) => (
            <div key={job.id} className="border rounded-lg p-4">
              <div className="flex items-start justify-between mb-3">
                <div className="flex items-center gap-3">
                  {getStatusIcon(job.status)}
                  <div>
                    <h3 className="font-semibold">Job #{job.id}</h3>
                    <p className="text-sm text-muted-foreground">Site ID: {job.site_id}</p>
                  </div>
                  <Badge className={getStatusColor(job.status)}>
                    {job.status}
                  </Badge>
                </div>
                {hasPermission('manage_scraping') && (job.status === 'running' || job.status === 'pending') && (
                  <Button
                    size="sm"
                    variant="destructive"
                    onClick={() => handleCancelJob(job.id)}
                  >
                    <StopCircle className="h-4 w-4 mr-1" />
                    Cancel
                  </Button>
                )}
              </div>

              {/* Progress Bar */}
              {(job.status === 'running' || job.status === 'pending') && (
                <div className="mb-3">
                  <div className="flex justify-between text-sm mb-1">
                    <span>Progress</span>
                    <span>{getProgressPercentage(job).toFixed(0)}%</span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div
                      className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                      style={{ width: `${getProgressPercentage(job)}%` }}
                    />
                  </div>
                </div>
              )}

              {/* Job Stats */}
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-3">
                <div className="text-center p-2 bg-muted/50 rounded">
                  <div className="text-lg font-semibold">{formatNumber(job.pages_scraped)}</div>
                  <div className="text-xs text-muted-foreground">Pages</div>
                </div>
                <div className="text-center p-2 bg-muted/50 rounded">
                  <div className="text-lg font-semibold">{formatNumber(job.videos_found)}</div>
                  <div className="text-xs text-muted-foreground">Found</div>
                </div>
                <div className="text-center p-2 bg-muted/50 rounded">
                  <div className="text-lg font-semibold">{formatNumber(job.videos_added)}</div>
                  <div className="text-xs text-muted-foreground">Added</div>
                </div>
                <div className="text-center p-2 bg-muted/50 rounded">
                  <div className="text-lg font-semibold">{formatNumber(job.videos_skipped)}</div>
                  <div className="text-xs text-muted-foreground">Skipped</div>
                </div>
              </div>

              {/* Job Details */}
              <div className="flex flex-wrap gap-4 text-sm text-muted-foreground">
                <span>Created: {formatDate(job.created_at)}</span>
                {job.started_at && <span>Started: {formatDate(job.started_at)}</span>}
                {job.completed_at && <span>Completed: {formatDate(job.completed_at)}</span>}
                {job.duration_seconds && <span>Duration: {formatDuration(job.duration_seconds)}</span>}
                {job.error_count > 0 && (
                  <span className="text-red-500">Errors: {job.error_count}</span>
                )}
              </div>

              {/* Error Message */}
              {job.error_message && (
                <div className="mt-3 p-2 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded text-sm text-red-700 dark:text-red-300">
                  <strong>Error:</strong> {job.error_message}
                </div>
              )}
            </div>
          ))}

          {jobs.length === 0 && !isLoading && (
            <div className="text-center py-8 text-muted-foreground">
              No jobs found. Start scraping some sites to see job activity here.
            </div>
          )}

          {isLoading && (
            <div className="text-center py-8">
              <RefreshCw className="h-6 w-6 animate-spin mx-auto mb-2" />
              <p className="text-muted-foreground">Loading jobs...</p>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
};
