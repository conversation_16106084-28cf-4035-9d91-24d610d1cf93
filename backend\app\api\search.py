"""
Search API endpoints.
"""

from fastapi import APIRouter, Query, Depends, HTTPException
from sqlalchemy.orm import Session
from sqlalchemy import or_, and_, desc, func
from typing import List, Optional
import logging

from app.models.database import get_db
from app.models.video import Video
from app.models.category import Category
from app.models.performer import Performer
from app.models.user_features import SearchLog
from app.schemas.video import VideoSchema, VideoListResponse

logger = logging.getLogger(__name__)

router = APIRouter()


@router.get("/", response_model=VideoListResponse)
async def search_videos(
    q: str = Query(..., min_length=1, description="Search query"),
    skip: int = Query(0, ge=0, description="Number of results to skip"),
    limit: int = Query(50, ge=1, le=100, description="Number of results to return"),
    categories: Optional[str] = Query(None, description="Filter by categories (comma-separated)"),
    performers: Optional[str] = Query(None, description="Filter by performers (comma-separated)"),
    min_duration: Optional[int] = Query(None, description="Minimum duration in seconds"),
    max_duration: Optional[int] = Query(None, description="Maximum duration in seconds"),
    min_resolution: Optional[int] = Query(None, description="Minimum resolution height"),
    min_rating: Optional[float] = Query(None, description="Minimum rating"),
    quality: Optional[str] = Query(None, description="Quality filter (hd, 4k, etc.)"),
    sort_by: Optional[str] = Query("relevance", description="Sort field"),
    sort_order: Optional[str] = Query("desc", description="Sort order (asc/desc)"),
    db: Session = Depends(get_db)
):
    """Search videos across all metadata fields."""
    try:
        # Build base query
        query = db.query(Video).filter(Video.is_active == True)

        # Full-text search across title and description
        search_terms = q.strip().split()
        search_conditions = []

        for term in search_terms:
            term_condition = or_(
                Video.title.ilike(f"%{term}%"),
                Video.description.ilike(f"%{term}%")
            )
            search_conditions.append(term_condition)

        # Apply search conditions (all terms must match)
        if search_conditions:
            query = query.filter(and_(*search_conditions))

        # Apply filters
        if categories:
            category_list = [cat.strip() for cat in categories.split(',') if cat.strip()]
            if category_list:
                query = query.join(Video.categories).filter(Category.slug.in_(category_list))

        if performers:
            performer_list = [perf.strip() for perf in performers.split(',') if perf.strip()]
            if performer_list:
                query = query.join(Video.performers).filter(Performer.slug.in_(performer_list))

        if min_duration:
            query = query.filter(Video.duration >= min_duration)

        if max_duration:
            query = query.filter(Video.duration <= max_duration)

        if min_resolution:
            query = query.filter(Video.resolution_height >= min_resolution)

        if min_rating:
            query = query.filter(Video.rating >= min_rating)

        if quality:
            if quality.lower() == '4k':
                query = query.filter(Video.is_4k == True)
            elif quality.lower() == 'hd':
                query = query.filter(Video.is_hd == True)
            elif quality.lower() == '1080p':
                query = query.filter(Video.resolution_height >= 1080)
            elif quality.lower() == '720p':
                query = query.filter(Video.resolution_height >= 720)

        # Apply sorting
        if sort_by == "relevance":
            # Simple relevance scoring based on title matches
            query = query.order_by(
                desc(func.length(Video.title) - func.length(func.replace(func.lower(Video.title), func.lower(q), ''))),
                desc(Video.view_count),
                desc(Video.rating)
            )
        else:
            sort_column = getattr(Video, sort_by, Video.date_added)
            if sort_order.lower() == "asc":
                query = query.order_by(sort_column)
            else:
                query = query.order_by(desc(sort_column))

        # Get total count
        total = query.count()

        # Apply pagination
        videos = query.offset(skip).limit(limit).all()

        # Log search query (optional - for analytics)
        # search_log = SearchLog(
        #     query=q,
        #     normalized_query=q.lower().strip(),
        #     results_count=total,
        #     category_filter=category,
        #     performer_filter=performer
        # )
        # db.add(search_log)
        # db.commit()

        return VideoListResponse(
            videos=[VideoSchema.model_validate(video) for video in videos],
            total=total,
            skip=skip,
            limit=limit,
            has_more=(skip + limit) < total
        )

    except Exception as e:
        logger.error(f"Error searching videos: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.get("/suggestions")
async def get_search_suggestions(
    q: str = Query(..., min_length=1, description="Partial search query"),
    limit: int = Query(10, ge=1, le=20, description="Number of suggestions to return"),
    db: Session = Depends(get_db)
):
    """Get search suggestions based on partial query."""
    try:
        suggestions = []

        # Get video title suggestions
        video_titles = db.query(Video.title).filter(
            Video.is_active == True,
            Video.title.ilike(f"%{q}%")
        ).limit(limit // 2).all()

        for title in video_titles:
            suggestions.append({
                "text": title[0],
                "type": "video_title"
            })

        # Get category suggestions
        categories = db.query(Category.name).filter(
            Category.is_active == True,
            Category.name.ilike(f"%{q}%")
        ).limit(limit // 4).all()

        for category in categories:
            suggestions.append({
                "text": category[0],
                "type": "category"
            })

        # Get performer suggestions
        performers = db.query(Performer.name).filter(
            Performer.is_active == True,
            or_(
                Performer.name.ilike(f"%{q}%"),
                Performer.stage_name.ilike(f"%{q}%")
            )
        ).limit(limit // 4).all()

        for performer in performers:
            suggestions.append({
                "text": performer[0],
                "type": "performer"
            })

        # Limit total suggestions
        suggestions = suggestions[:limit]

        return {
            "suggestions": suggestions,
            "query": q
        }

    except Exception as e:
        logger.error(f"Error getting search suggestions: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.get("/categories")
async def get_categories(
    featured_only: bool = Query(False, description="Return only featured categories"),
    db: Session = Depends(get_db)
):
    """Get all available categories."""
    try:
        query = db.query(Category).filter(Category.is_active == True)

        if featured_only:
            query = query.filter(Category.is_featured == True)

        categories = query.order_by(Category.sort_order, Category.name).all()

        result = []
        for category in categories:
            result.append({
                "id": category.id,
                "name": category.name,
                "slug": category.slug,
                "display_name": category.display_name,
                "video_count": category.video_count,
                "color": category.color,
                "icon": category.icon,
                "is_featured": category.is_featured
            })

        return {"categories": result}

    except Exception as e:
        logger.error(f"Error fetching categories: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.get("/performers")
async def get_performers(
    q: Optional[str] = Query(None, description="Filter performers by name"),
    limit: int = Query(50, ge=1, le=100, description="Number of performers to return"),
    skip: int = Query(0, ge=0, description="Number of performers to skip"),
    sort_by: Optional[str] = Query("popularity_score", description="Sort field"),
    sort_order: Optional[str] = Query("desc", description="Sort order (asc/desc)"),
    db: Session = Depends(get_db)
):
    """Get list of performers/actors."""
    try:
        query = db.query(Performer).filter(Performer.is_active == True)

        # Apply search filter
        if q:
            query = query.filter(
                or_(
                    Performer.name.ilike(f"%{q}%"),
                    Performer.stage_name.ilike(f"%{q}%")
                )
            )

        # Apply sorting
        sort_column = getattr(Performer, sort_by, Performer.popularity_score)
        if sort_order.lower() == "asc":
            query = query.order_by(sort_column)
        else:
            query = query.order_by(desc(sort_column))

        # Get total count
        total = query.count()

        # Apply pagination
        performers = query.offset(skip).limit(limit).all()

        result = []
        for performer in performers:
            result.append({
                "id": performer.id,
                "name": performer.name,
                "stage_name": performer.stage_name,
                "slug": performer.slug,
                "video_count": performer.video_count,
                "popularity_score": performer.popularity_score,
                "is_verified": performer.is_verified,
                "is_featured": performer.is_featured,
                "profile_image_url": performer.profile_image_url
            })

        return {
            "performers": result,
            "total": total,
            "skip": skip,
            "limit": limit,
            "has_more": (skip + limit) < total
        }

    except Exception as e:
        logger.error(f"Error fetching performers: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")
