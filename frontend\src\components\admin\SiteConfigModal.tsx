/**
 * Site configuration modal for adding and editing scraping sites
 */

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../ui/Card';
import { Button } from '../ui/Button';
import { Input } from '../ui/Input';
import { Label } from '../ui/Label';
import { Badge } from '../ui/Badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '../ui/Tabs';
import {
  X,
  Globe,
  Settings,
  Shield,
  Clock,
  Zap,
  CheckCircle,
  AlertTriangle,
  Info,
  Code,
  Key,
  TestTube,
  Plus,
  Sparkles
} from 'lucide-react';
import { adminApi, ScrapingSite, CreateSiteRequest } from '../../services/adminApi';
import { SiteTemplateSelector } from './SiteTemplateSelector';
import { SiteTemplate } from '../../data/siteTemplates';

interface SiteConfigModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSave: (site: ScrapingSite) => void;
  site?: ScrapingSite | null; // null for create, ScrapingSite for edit
}

interface FormData extends CreateSiteRequest {
  // Additional UI-only fields
}

const defaultFormData: FormData = {
  name: '',
  base_url: '',
  description: '',
  is_active: true,
  priority: 5,
  scraping_interval_hours: 24,
  max_pages_per_scrape: 10,
  site_type: 'video',
  content_rating: 'adult',
  language: 'en',
  country: '',
  rate_limit_delay: 1.0,
  requires_auth: false,
  scraping_config: {},
  headers: {},
  auth_config: {}
};

export const SiteConfigModal: React.FC<SiteConfigModalProps> = ({
  isOpen,
  onClose,
  onSave,
  site
}) => {
  const [formData, setFormData] = useState<FormData>(defaultFormData);
  const [currentTab, setCurrentTab] = useState('basic');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');
  const [validationErrors, setValidationErrors] = useState<Record<string, string>>({});
  const [isTestingConnection, setIsTestingConnection] = useState(false);
  const [testResult, setTestResult] = useState<{ success: boolean; message: string } | null>(null);
  const [isValidatingRules, setIsValidatingRules] = useState(false);
  const [validationResult, setValidationResult] = useState<{ success: boolean; message: string; validation_results?: any } | null>(null);
  const [showTemplateSelector, setShowTemplateSelector] = useState(false);

  const isEditMode = !!site;

  useEffect(() => {
    if (isOpen) {
      if (site) {
        // Edit mode - populate form with existing data
        setFormData({
          name: site.name,
          base_url: site.base_url,
          description: site.description || '',
          is_active: site.is_active,
          priority: site.priority,
          scraping_interval_hours: site.scraping_interval_hours,
          max_pages_per_scrape: site.max_pages_per_scrape,
          site_type: site.site_type,
          content_rating: site.content_rating,
          language: site.language,
          country: site.country || '',
          rate_limit_delay: site.rate_limit_delay,
          requires_auth: site.requires_auth,
          scraping_config: site.scraping_config || {},
          headers: site.headers || {},
          auth_config: site.auth_config || {}
        });
      } else {
        // Create mode - reset to defaults
        setFormData(defaultFormData);
      }
      setCurrentTab('basic');
      setError('');
      setValidationErrors({});
      setTestResult(null);
      setValidationResult(null);
      setShowTemplateSelector(false);
    }
  }, [isOpen, site]);

  const validateForm = (): boolean => {
    const errors: Record<string, string> = {};

    if (!formData.name.trim()) {
      errors.name = 'Site name is required';
    }

    if (!formData.base_url.trim()) {
      errors.base_url = 'Base URL is required';
    } else {
      try {
        new URL(formData.base_url);
      } catch {
        errors.base_url = 'Please enter a valid URL';
      }
    }

    if (formData.priority < 1 || formData.priority > 10) {
      errors.priority = 'Priority must be between 1 and 10';
    }

    if (formData.scraping_interval_hours < 1 || formData.scraping_interval_hours > 168) {
      errors.scraping_interval_hours = 'Interval must be between 1 and 168 hours';
    }

    if (formData.max_pages_per_scrape < 1 || formData.max_pages_per_scrape > 100) {
      errors.max_pages_per_scrape = 'Pages per scrape must be between 1 and 100';
    }

    if (formData.rate_limit_delay < 0.1 || formData.rate_limit_delay > 10) {
      errors.rate_limit_delay = 'Rate limit delay must be between 0.1 and 10 seconds';
    }

    setValidationErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleInputChange = (field: keyof FormData, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    
    // Clear validation error for this field
    if (validationErrors[field]) {
      setValidationErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors[field];
        return newErrors;
      });
    }
  };

  const testConnection = async () => {
    if (!formData.base_url.trim()) {
      setTestResult({ success: false, message: 'Please enter a URL first' });
      return;
    }

    setIsTestingConnection(true);
    setTestResult(null);

    try {
      const result = await adminApi.testSite({
        url: formData.base_url,
        headers: formData.headers,
        auth_config: formData.requires_auth ? formData.auth_config : undefined
      });
      
      setTestResult({
        success: result.success,
        message: result.message || (result.success ? 'Connection successful!' : 'Connection failed')
      });
    } catch (err) {
      setTestResult({
        success: false,
        message: err instanceof Error ? err.message : 'Connection test failed'
      });
    } finally {
      setIsTestingConnection(false);
    }
  };

  const validateScrapingRules = async () => {
    if (!formData.base_url.trim()) {
      setValidationResult({ success: false, message: 'Please enter a URL first' });
      return;
    }

    if (!formData.scraping_config || Object.keys(formData.scraping_config).length === 0) {
      setValidationResult({ success: false, message: 'Please configure at least one CSS selector' });
      return;
    }

    setIsValidatingRules(true);
    setValidationResult(null);

    try {
      const result = await adminApi.validateScrapingRules({
        url: formData.base_url,
        scraping_config: formData.scraping_config
      });

      setValidationResult(result);
    } catch (err) {
      setValidationResult({
        success: false,
        message: err instanceof Error ? err.message : 'Validation failed'
      });
    } finally {
      setIsValidatingRules(false);
    }
  };

  const applyTemplate = (template: SiteTemplate) => {
    setFormData({
      ...formData,
      site_type: template.config.site_type,
      content_rating: template.config.content_rating,
      language: template.config.language,
      priority: template.config.priority,
      scraping_interval_hours: template.config.scraping_interval_hours,
      max_pages_per_scrape: template.config.max_pages_per_scrape,
      rate_limit_delay: template.config.rate_limit_delay,
      requires_auth: template.config.requires_auth,
      scraping_config: template.config.scraping_config,
      headers: template.config.headers
    });
    setShowTemplateSelector(false);
  };

  const handleSave = async () => {
    if (!validateForm()) {
      return;
    }

    setIsLoading(true);
    setError('');

    try {
      let savedSite: ScrapingSite;
      
      if (isEditMode && site) {
        savedSite = await adminApi.updateSite(site.id, formData);
      } else {
        savedSite = await adminApi.createSite(formData);
      }

      onSave(savedSite);
      onClose();
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to save site');
    } finally {
      setIsLoading(false);
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
      <div className="bg-background rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-hidden">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b">
          <div>
            <h2 className="text-xl font-semibold">
              {isEditMode ? 'Edit Scraping Site' : 'Add New Scraping Site'}
            </h2>
            <p className="text-sm text-muted-foreground mt-1">
              {isEditMode ? 'Update site configuration and settings' : 'Configure a new video scraping source'}
            </p>
          </div>
          <div className="flex items-center gap-2">
            {!isEditMode && (
              <Button variant="outline" size="sm" onClick={() => setShowTemplateSelector(true)}>
                <Sparkles className="h-4 w-4 mr-2" />
                Use Template
              </Button>
            )}
            <Button variant="ghost" size="sm" onClick={onClose}>
              <X className="h-4 w-4" />
            </Button>
          </div>
        </div>

        {/* Content */}
        <div className="flex-1 overflow-y-auto max-h-[calc(90vh-140px)]">
          <Tabs value={currentTab} onValueChange={setCurrentTab} className="p-6">
            <TabsList className="grid w-full grid-cols-4">
              <TabsTrigger value="basic" className="flex items-center gap-2">
                <Globe className="h-4 w-4" />
                Basic
              </TabsTrigger>
              <TabsTrigger value="scraping" className="flex items-center gap-2">
                <Settings className="h-4 w-4" />
                Scraping
              </TabsTrigger>
              <TabsTrigger value="advanced" className="flex items-center gap-2">
                <Code className="h-4 w-4" />
                Advanced
              </TabsTrigger>
              <TabsTrigger value="auth" className="flex items-center gap-2">
                <Shield className="h-4 w-4" />
                Auth
              </TabsTrigger>
            </TabsList>

            {/* Basic Configuration */}
            <TabsContent value="basic" className="space-y-6 mt-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-4">
                  <div>
                    <Label htmlFor="name">Site Name *</Label>
                    <Input
                      id="name"
                      value={formData.name}
                      onChange={(e) => handleInputChange('name', e.target.value)}
                      placeholder="e.g., Example Video Site"
                      className={validationErrors.name ? 'border-destructive' : ''}
                    />
                    {validationErrors.name && (
                      <p className="text-sm text-destructive mt-1">{validationErrors.name}</p>
                    )}
                  </div>

                  <div>
                    <Label htmlFor="base_url">Base URL *</Label>
                    <div className="flex gap-2">
                      <Input
                        id="base_url"
                        value={formData.base_url}
                        onChange={(e) => handleInputChange('base_url', e.target.value)}
                        placeholder="https://example.com"
                        className={validationErrors.base_url ? 'border-destructive' : ''}
                      />
                      <Button
                        type="button"
                        variant="outline"
                        onClick={testConnection}
                        disabled={isTestingConnection}
                        className="shrink-0"
                      >
                        <TestTube className={`h-4 w-4 ${isTestingConnection ? 'animate-spin' : ''}`} />
                      </Button>
                    </div>
                    {validationErrors.base_url && (
                      <p className="text-sm text-destructive mt-1">{validationErrors.base_url}</p>
                    )}
                    {testResult && (
                      <div className={`flex items-center gap-2 mt-2 text-sm ${
                        testResult.success ? 'text-green-600' : 'text-destructive'
                      }`}>
                        {testResult.success ? (
                          <CheckCircle className="h-4 w-4" />
                        ) : (
                          <AlertTriangle className="h-4 w-4" />
                        )}
                        {testResult.message}
                      </div>
                    )}
                  </div>

                  <div>
                    <Label htmlFor="description">Description</Label>
                    <textarea
                      id="description"
                      value={formData.description}
                      onChange={(e) => handleInputChange('description', e.target.value)}
                      placeholder="Brief description of the site..."
                      className="w-full px-3 py-2 border rounded-md bg-background resize-none"
                      rows={3}
                    />
                  </div>
                </div>

                <div className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="priority">Priority (1-10)</Label>
                      <Input
                        id="priority"
                        type="number"
                        min="1"
                        max="10"
                        value={formData.priority}
                        onChange={(e) => handleInputChange('priority', parseInt(e.target.value) || 1)}
                        className={validationErrors.priority ? 'border-destructive' : ''}
                      />
                      {validationErrors.priority && (
                        <p className="text-sm text-destructive mt-1">{validationErrors.priority}</p>
                      )}
                    </div>

                    <div>
                      <Label htmlFor="language">Language</Label>
                      <select
                        id="language"
                        value={formData.language}
                        onChange={(e) => handleInputChange('language', e.target.value)}
                        className="w-full px-3 py-2 border rounded-md bg-background"
                      >
                        <option value="en">English</option>
                        <option value="es">Spanish</option>
                        <option value="fr">French</option>
                        <option value="de">German</option>
                        <option value="it">Italian</option>
                        <option value="pt">Portuguese</option>
                        <option value="ru">Russian</option>
                        <option value="ja">Japanese</option>
                        <option value="ko">Korean</option>
                        <option value="zh">Chinese</option>
                      </select>
                    </div>
                  </div>

                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="site_type">Site Type</Label>
                      <select
                        id="site_type"
                        value={formData.site_type}
                        onChange={(e) => handleInputChange('site_type', e.target.value)}
                        className="w-full px-3 py-2 border rounded-md bg-background"
                      >
                        <option value="video">Video</option>
                        <option value="image">Image</option>
                        <option value="mixed">Mixed</option>
                      </select>
                    </div>

                    <div>
                      <Label htmlFor="content_rating">Content Rating</Label>
                      <select
                        id="content_rating"
                        value={formData.content_rating}
                        onChange={(e) => handleInputChange('content_rating', e.target.value)}
                        className="w-full px-3 py-2 border rounded-md bg-background"
                      >
                        <option value="adult">Adult</option>
                        <option value="general">General</option>
                        <option value="mixed">Mixed</option>
                      </select>
                    </div>
                  </div>

                  <div>
                    <Label htmlFor="country">Country (Optional)</Label>
                    <Input
                      id="country"
                      value={formData.country}
                      onChange={(e) => handleInputChange('country', e.target.value)}
                      placeholder="e.g., US, UK, DE"
                      maxLength={10}
                    />
                  </div>

                  <div className="flex items-center gap-2">
                    <input
                      type="checkbox"
                      id="is_active"
                      checked={formData.is_active}
                      onChange={(e) => handleInputChange('is_active', e.target.checked)}
                      className="rounded"
                    />
                    <Label htmlFor="is_active">Active (start scraping immediately)</Label>
                  </div>
                </div>
              </div>
            </TabsContent>

            {/* Scraping Configuration */}
            <TabsContent value="scraping" className="space-y-6 mt-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-4">
                  <div>
                    <Label htmlFor="scraping_interval_hours">Scraping Interval (hours)</Label>
                    <Input
                      id="scraping_interval_hours"
                      type="number"
                      min="1"
                      max="168"
                      value={formData.scraping_interval_hours}
                      onChange={(e) => handleInputChange('scraping_interval_hours', parseInt(e.target.value) || 24)}
                      className={validationErrors.scraping_interval_hours ? 'border-destructive' : ''}
                    />
                    {validationErrors.scraping_interval_hours && (
                      <p className="text-sm text-destructive mt-1">{validationErrors.scraping_interval_hours}</p>
                    )}
                    <p className="text-xs text-muted-foreground mt-1">How often to scrape this site (1-168 hours)</p>
                  </div>

                  <div>
                    <Label htmlFor="max_pages_per_scrape">Max Pages per Scrape</Label>
                    <Input
                      id="max_pages_per_scrape"
                      type="number"
                      min="1"
                      max="100"
                      value={formData.max_pages_per_scrape}
                      onChange={(e) => handleInputChange('max_pages_per_scrape', parseInt(e.target.value) || 10)}
                      className={validationErrors.max_pages_per_scrape ? 'border-destructive' : ''}
                    />
                    {validationErrors.max_pages_per_scrape && (
                      <p className="text-sm text-destructive mt-1">{validationErrors.max_pages_per_scrape}</p>
                    )}
                    <p className="text-xs text-muted-foreground mt-1">Maximum pages to scrape per session (1-100)</p>
                  </div>

                  <div>
                    <Label htmlFor="rate_limit_delay">Rate Limit Delay (seconds)</Label>
                    <Input
                      id="rate_limit_delay"
                      type="number"
                      min="0.1"
                      max="10"
                      step="0.1"
                      value={formData.rate_limit_delay}
                      onChange={(e) => handleInputChange('rate_limit_delay', parseFloat(e.target.value) || 1.0)}
                      className={validationErrors.rate_limit_delay ? 'border-destructive' : ''}
                    />
                    {validationErrors.rate_limit_delay && (
                      <p className="text-sm text-destructive mt-1">{validationErrors.rate_limit_delay}</p>
                    )}
                    <p className="text-xs text-muted-foreground mt-1">Delay between requests to avoid being blocked</p>
                  </div>
                </div>

                <div className="space-y-4">
                  <div>
                    <Label>Scraping Rules</Label>
                    <div className="border rounded-lg p-4 bg-muted/30">
                      <div className="flex items-center gap-2 mb-3">
                        <Code className="h-4 w-4" />
                        <span className="text-sm font-medium">CSS Selectors</span>
                      </div>
                      <div className="space-y-3 text-sm">
                        <div>
                          <Label htmlFor="video_selector" className="text-xs">Video Links Selector</Label>
                          <Input
                            id="video_selector"
                            placeholder="a[href*='/video/']"
                            className="text-xs"
                            value={(formData.scraping_config as any)?.video_selector || ''}
                            onChange={(e) => handleInputChange('scraping_config', {
                              ...formData.scraping_config,
                              video_selector: e.target.value
                            })}
                          />
                        </div>
                        <div>
                          <Label htmlFor="title_selector" className="text-xs">Title Selector</Label>
                          <Input
                            id="title_selector"
                            placeholder="h1.video-title, .title"
                            className="text-xs"
                            value={(formData.scraping_config as any)?.title_selector || ''}
                            onChange={(e) => handleInputChange('scraping_config', {
                              ...formData.scraping_config,
                              title_selector: e.target.value
                            })}
                          />
                        </div>
                        <div>
                          <Label htmlFor="thumbnail_selector" className="text-xs">Thumbnail Selector</Label>
                          <Input
                            id="thumbnail_selector"
                            placeholder="img.thumbnail, .preview-image"
                            className="text-xs"
                            value={(formData.scraping_config as any)?.thumbnail_selector || ''}
                            onChange={(e) => handleInputChange('scraping_config', {
                              ...formData.scraping_config,
                              thumbnail_selector: e.target.value
                            })}
                          />
                        </div>
                      </div>
                    </div>
                  </div>

                  <div>
                    <Label>Performance Settings</Label>
                    <div className="border rounded-lg p-4 bg-muted/30 space-y-3">
                      <div className="flex items-center justify-between">
                        <span className="text-sm">Concurrent Requests</span>
                        <select
                          value={(formData.scraping_config as any)?.concurrent_requests || 1}
                          onChange={(e) => handleInputChange('scraping_config', {
                            ...formData.scraping_config,
                            concurrent_requests: parseInt(e.target.value)
                          })}
                          className="px-2 py-1 border rounded text-sm bg-background"
                        >
                          <option value={1}>1 (Safe)</option>
                          <option value={2}>2 (Moderate)</option>
                          <option value={3}>3 (Aggressive)</option>
                        </select>
                      </div>
                      <div className="flex items-center justify-between">
                        <span className="text-sm">Request Timeout</span>
                        <select
                          value={(formData.scraping_config as any)?.timeout || 30}
                          onChange={(e) => handleInputChange('scraping_config', {
                            ...formData.scraping_config,
                            timeout: parseInt(e.target.value)
                          })}
                          className="px-2 py-1 border rounded text-sm bg-background"
                        >
                          <option value={10}>10 seconds</option>
                          <option value={30}>30 seconds</option>
                          <option value={60}>60 seconds</option>
                        </select>
                      </div>
                    </div>
                  </div>

                  {/* Validation Section */}
                  <div>
                    <div className="flex items-center justify-between mb-4">
                      <Label>Test Scraping Rules</Label>
                      <Button
                        type="button"
                        variant="outline"
                        onClick={validateScrapingRules}
                        disabled={isValidatingRules || !formData.base_url.trim()}
                        className="shrink-0"
                      >
                        <TestTube className={`h-4 w-4 mr-2 ${isValidatingRules ? 'animate-spin' : ''}`} />
                        {isValidatingRules ? 'Validating...' : 'Validate Rules'}
                      </Button>
                    </div>

                    {validationResult && (
                      <div className={`border rounded-lg p-4 ${
                        validationResult.success
                          ? 'bg-green-50 dark:bg-green-900/20 border-green-200 dark:border-green-800'
                          : 'bg-red-50 dark:bg-red-900/20 border-red-200 dark:border-red-800'
                      }`}>
                        <div className={`flex items-start gap-2 mb-3 ${
                          validationResult.success ? 'text-green-800 dark:text-green-200' : 'text-red-800 dark:text-red-200'
                        }`}>
                          {validationResult.success ? (
                            <CheckCircle className="h-5 w-5 mt-0.5" />
                          ) : (
                            <AlertTriangle className="h-5 w-5 mt-0.5" />
                          )}
                          <div>
                            <p className="font-medium">
                              {validationResult.success ? 'Validation Successful' : 'Validation Failed'}
                            </p>
                            <p className="text-sm mt-1">{validationResult.message}</p>
                          </div>
                        </div>

                        {validationResult.validation_results && (
                          <div className="space-y-3 text-sm">
                            {Object.entries(validationResult.validation_results).map(([key, result]: [string, any]) => (
                              <div key={key} className="bg-white dark:bg-gray-800 rounded p-3 border">
                                <div className="flex items-center justify-between mb-2">
                                  <span className="font-medium capitalize">{key.replace('_', ' ')}</span>
                                  <Badge variant="outline">{result.count} found</Badge>
                                </div>
                                {result.samples && result.samples.length > 0 && (
                                  <div className="space-y-1">
                                    <p className="text-xs text-muted-foreground">Sample results:</p>
                                    {result.samples.map((sample: string, index: number) => (
                                      <div key={index} className="text-xs bg-muted rounded px-2 py-1 truncate">
                                        {sample}
                                      </div>
                                    ))}
                                  </div>
                                )}
                              </div>
                            ))}
                          </div>
                        )}
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </TabsContent>

            {/* Advanced Configuration */}
            <TabsContent value="advanced" className="space-y-6 mt-6">
              <div className="grid grid-cols-1 gap-6">
                <div>
                  <Label>Custom HTTP Headers</Label>
                  <div className="border rounded-lg p-4 bg-muted/30">
                    <div className="space-y-3">
                      {Object.entries(formData.headers || {}).map(([key, value], index) => (
                        <div key={index} className="flex gap-2">
                          <Input
                            placeholder="Header name"
                            value={key}
                            onChange={(e) => {
                              const newHeaders = { ...formData.headers };
                              delete newHeaders[key];
                              newHeaders[e.target.value] = value;
                              handleInputChange('headers', newHeaders);
                            }}
                            className="flex-1"
                          />
                          <Input
                            placeholder="Header value"
                            value={value}
                            onChange={(e) => {
                              handleInputChange('headers', {
                                ...formData.headers,
                                [key]: e.target.value
                              });
                            }}
                            className="flex-1"
                          />
                          <Button
                            type="button"
                            variant="outline"
                            size="sm"
                            onClick={() => {
                              const newHeaders = { ...formData.headers };
                              delete newHeaders[key];
                              handleInputChange('headers', newHeaders);
                            }}
                          >
                            <X className="h-4 w-4" />
                          </Button>
                        </div>
                      ))}
                      <Button
                        type="button"
                        variant="outline"
                        size="sm"
                        onClick={() => {
                          handleInputChange('headers', {
                            ...formData.headers,
                            '': ''
                          });
                        }}
                      >
                        <Plus className="h-4 w-4 mr-2" />
                        Add Header
                      </Button>
                    </div>
                  </div>
                  <p className="text-xs text-muted-foreground mt-2">
                    Custom headers to send with requests (e.g., User-Agent, Referer)
                  </p>
                </div>

                <div>
                  <Label>URL Patterns & Filters</Label>
                  <div className="border rounded-lg p-4 bg-muted/30 space-y-4">
                    <div>
                      <Label htmlFor="url_patterns" className="text-sm">Allowed URL Patterns</Label>
                      <textarea
                        id="url_patterns"
                        placeholder="One pattern per line, e.g.:&#10;/video/*&#10;/watch/*"
                        className="w-full px-3 py-2 border rounded-md bg-background resize-none text-sm"
                        rows={3}
                        value={(formData.scraping_config as any)?.url_patterns?.join('\n') || ''}
                        onChange={(e) => handleInputChange('scraping_config', {
                          ...formData.scraping_config,
                          url_patterns: e.target.value.split('\n').filter(p => p.trim())
                        })}
                      />
                    </div>
                    <div>
                      <Label htmlFor="exclude_patterns" className="text-sm">Excluded URL Patterns</Label>
                      <textarea
                        id="exclude_patterns"
                        placeholder="One pattern per line, e.g.:&#10;/ads/*&#10;/popup/*"
                        className="w-full px-3 py-2 border rounded-md bg-background resize-none text-sm"
                        rows={3}
                        value={(formData.scraping_config as any)?.exclude_patterns?.join('\n') || ''}
                        onChange={(e) => handleInputChange('scraping_config', {
                          ...formData.scraping_config,
                          exclude_patterns: e.target.value.split('\n').filter(p => p.trim())
                        })}
                      />
                    </div>
                  </div>
                </div>

                <div>
                  <Label>Quality Filters</Label>
                  <div className="border rounded-lg p-4 bg-muted/30">
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <Label htmlFor="min_duration" className="text-sm">Min Duration (seconds)</Label>
                        <Input
                          id="min_duration"
                          type="number"
                          min="0"
                          placeholder="0"
                          value={(formData.scraping_config as any)?.min_duration || ''}
                          onChange={(e) => handleInputChange('scraping_config', {
                            ...formData.scraping_config,
                            min_duration: parseInt(e.target.value) || 0
                          })}
                        />
                      </div>
                      <div>
                        <Label htmlFor="min_resolution" className="text-sm">Min Resolution Height</Label>
                        <select
                          id="min_resolution"
                          value={(formData.scraping_config as any)?.min_resolution || ''}
                          onChange={(e) => handleInputChange('scraping_config', {
                            ...formData.scraping_config,
                            min_resolution: e.target.value ? parseInt(e.target.value) : undefined
                          })}
                          className="w-full px-3 py-2 border rounded-md bg-background"
                        >
                          <option value="">No minimum</option>
                          <option value="480">480p</option>
                          <option value="720">720p (HD)</option>
                          <option value="1080">1080p (Full HD)</option>
                          <option value="2160">2160p (4K)</option>
                        </select>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </TabsContent>

            {/* Authentication Configuration */}
            <TabsContent value="auth" className="space-y-6 mt-6">
              <div className="space-y-6">
                <div className="flex items-center gap-3">
                  <input
                    type="checkbox"
                    id="requires_auth"
                    checked={formData.requires_auth}
                    onChange={(e) => handleInputChange('requires_auth', e.target.checked)}
                    className="rounded"
                  />
                  <Label htmlFor="requires_auth" className="text-base">This site requires authentication</Label>
                </div>

                {formData.requires_auth && (
                  <div className="space-y-6 pl-6 border-l-2 border-muted">
                    <div>
                      <Label>Authentication Method</Label>
                      <select
                        value={(formData.auth_config as any)?.method || 'basic'}
                        onChange={(e) => handleInputChange('auth_config', {
                          ...formData.auth_config,
                          method: e.target.value
                        })}
                        className="w-full px-3 py-2 border rounded-md bg-background mt-2"
                      >
                        <option value="basic">Basic Authentication</option>
                        <option value="form">Form-based Login</option>
                        <option value="api_key">API Key</option>
                        <option value="oauth">OAuth 2.0</option>
                      </select>
                    </div>

                    {(formData.auth_config as any)?.method === 'basic' && (
                      <div className="grid grid-cols-2 gap-4">
                        <div>
                          <Label htmlFor="auth_username">Username</Label>
                          <Input
                            id="auth_username"
                            type="text"
                            value={(formData.auth_config as any)?.username || ''}
                            onChange={(e) => handleInputChange('auth_config', {
                              ...formData.auth_config,
                              username: e.target.value
                            })}
                          />
                        </div>
                        <div>
                          <Label htmlFor="auth_password">Password</Label>
                          <Input
                            id="auth_password"
                            type="password"
                            value={(formData.auth_config as any)?.password || ''}
                            onChange={(e) => handleInputChange('auth_config', {
                              ...formData.auth_config,
                              password: e.target.value
                            })}
                          />
                        </div>
                      </div>
                    )}

                    {(formData.auth_config as any)?.method === 'form' && (
                      <div className="space-y-4">
                        <div>
                          <Label htmlFor="login_url">Login URL</Label>
                          <Input
                            id="login_url"
                            type="url"
                            placeholder="https://example.com/login"
                            value={(formData.auth_config as any)?.login_url || ''}
                            onChange={(e) => handleInputChange('auth_config', {
                              ...formData.auth_config,
                              login_url: e.target.value
                            })}
                          />
                        </div>
                        <div className="grid grid-cols-2 gap-4">
                          <div>
                            <Label htmlFor="form_username">Username</Label>
                            <Input
                              id="form_username"
                              type="text"
                              value={(formData.auth_config as any)?.username || ''}
                              onChange={(e) => handleInputChange('auth_config', {
                                ...formData.auth_config,
                                username: e.target.value
                              })}
                            />
                          </div>
                          <div>
                            <Label htmlFor="form_password">Password</Label>
                            <Input
                              id="form_password"
                              type="password"
                              value={(formData.auth_config as any)?.password || ''}
                              onChange={(e) => handleInputChange('auth_config', {
                                ...formData.auth_config,
                                password: e.target.value
                              })}
                            />
                          </div>
                        </div>
                        <div className="grid grid-cols-2 gap-4">
                          <div>
                            <Label htmlFor="username_field">Username Field Name</Label>
                            <Input
                              id="username_field"
                              placeholder="username, email, login"
                              value={(formData.auth_config as any)?.username_field || 'username'}
                              onChange={(e) => handleInputChange('auth_config', {
                                ...formData.auth_config,
                                username_field: e.target.value
                              })}
                            />
                          </div>
                          <div>
                            <Label htmlFor="password_field">Password Field Name</Label>
                            <Input
                              id="password_field"
                              placeholder="password, pass"
                              value={(formData.auth_config as any)?.password_field || 'password'}
                              onChange={(e) => handleInputChange('auth_config', {
                                ...formData.auth_config,
                                password_field: e.target.value
                              })}
                            />
                          </div>
                        </div>
                      </div>
                    )}

                    {(formData.auth_config as any)?.method === 'api_key' && (
                      <div className="space-y-4">
                        <div>
                          <Label htmlFor="api_key">API Key</Label>
                          <Input
                            id="api_key"
                            type="password"
                            value={(formData.auth_config as any)?.api_key || ''}
                            onChange={(e) => handleInputChange('auth_config', {
                              ...formData.auth_config,
                              api_key: e.target.value
                            })}
                          />
                        </div>
                        <div>
                          <Label htmlFor="api_key_header">API Key Header Name</Label>
                          <Input
                            id="api_key_header"
                            placeholder="X-API-Key, Authorization"
                            value={(formData.auth_config as any)?.api_key_header || 'X-API-Key'}
                            onChange={(e) => handleInputChange('auth_config', {
                              ...formData.auth_config,
                              api_key_header: e.target.value
                            })}
                          />
                        </div>
                      </div>
                    )}

                    <div className="bg-amber-50 dark:bg-amber-900/20 border border-amber-200 dark:border-amber-800 rounded-lg p-4">
                      <div className="flex items-start gap-2">
                        <AlertTriangle className="h-5 w-5 text-amber-600 dark:text-amber-400 mt-0.5" />
                        <div className="text-sm">
                          <p className="font-medium text-amber-800 dark:text-amber-200">Security Notice</p>
                          <p className="text-amber-700 dark:text-amber-300 mt-1">
                            Authentication credentials are encrypted and stored securely. Only use accounts
                            specifically created for scraping purposes.
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>
                )}

                {!formData.requires_auth && (
                  <div className="text-center py-8 text-muted-foreground">
                    <Shield className="h-12 w-12 mx-auto mb-4 opacity-50" />
                    <p>No authentication required for this site</p>
                    <p className="text-sm mt-2">Enable authentication above if the site requires login</p>
                  </div>
                )}
              </div>
            </TabsContent>
          </Tabs>
        </div>

        {/* Footer */}
        <div className="flex items-center justify-between p-6 border-t bg-muted/30">
          {error && (
            <div className="flex items-center gap-2 text-destructive text-sm">
              <AlertTriangle className="h-4 w-4" />
              {error}
            </div>
          )}
          <div className="flex items-center gap-3 ml-auto">
            <Button variant="outline" onClick={onClose} disabled={isLoading}>
              Cancel
            </Button>
            <Button onClick={handleSave} disabled={isLoading}>
              {isLoading ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2" />
                  {isEditMode ? 'Updating...' : 'Creating...'}
                </>
              ) : (
                <>
                  <CheckCircle className="h-4 w-4 mr-2" />
                  {isEditMode ? 'Update Site' : 'Create Site'}
                </>
              )}
            </Button>
          </div>
        </div>
      </div>

      {/* Template Selector */}
      <SiteTemplateSelector
        isOpen={showTemplateSelector}
        onClose={() => setShowTemplateSelector(false)}
        onSelectTemplate={applyTemplate}
      />
    </div>
  );
};
