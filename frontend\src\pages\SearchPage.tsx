import React, { useEffect, useState } from 'react'
import { useSearchParams } from 'react-router-dom'
import { Search } from 'lucide-react'
import { SearchBar } from '@/components/search/SearchBar'
import { SearchResults } from '@/components/search/SearchResults'
import { useSearchStore } from '@/store'
import { useSearchOptimization } from '@/hooks/useSearchOptimization'

export function SearchPage() {
  const [searchParams, setSearchParams] = useSearchParams()
  const query = searchParams.get('q') || ''
  const [currentPage, setCurrentPage] = useState(1)

  const {
    filters,
    setQuery,
    setFilters,
  } = useSearchStore()

  // Use optimized search hook
  const {
    results,
    totalResults,
    hasMore,
    isLoading,
    error,
    searchMetrics,
    analytics,
    retry,
    isOptimized,
    needsOptimization,
  } = useSearchOptimization({
    query,
    filters,
    enabled: !!query.trim(),
  })

  // Update store when query changes
  useEffect(() => {
    setQuery(query)
  }, [query, setQuery])

  const handleSearch = (newQuery: string) => {
    setSearchParams({ q: newQuery })
    setCurrentPage(1)
  }

  const handleFiltersChange = (newFilters: typeof filters) => {
    setFilters(newFilters)
    setCurrentPage(1)
  }

  const handleLoadMore = () => {
    setCurrentPage(prev => prev + 1)
  }

  const handleRetry = () => {
    retry()
  }

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="text-center space-y-4">
        <h1 className="text-3xl font-bold">Search Videos</h1>
        <p className="text-muted-foreground">
          Find videos by title, category, performer, or description
        </p>
      </div>

      {/* Search Bar */}
      <div className="max-w-2xl mx-auto">
        <SearchBar
          onSearch={handleSearch}
          placeholder="Search for videos, categories, performers..."
          className="w-full"
          showQuickFilters={!query}
          initialValue={query}
        />
      </div>

      {/* Search Results */}
      <SearchResults
        query={query}
        results={results}
        totalResults={totalResults}
        hasMore={hasMore}
        isLoading={isLoading}
        error={error}
        filters={filters}
        onFiltersChange={handleFiltersChange}
        onLoadMore={handleLoadMore}
        onRetry={handleRetry}
        searchTime={searchMetrics?.searchTime}
      />
    </div>
  )
}
