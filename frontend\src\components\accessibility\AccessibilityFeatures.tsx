import React, { useState, useEffect } from 'react'
import { Eye, EyeOff, Type, Contrast, Volume2, MousePointer, Keyboard, Zap } from 'lucide-react'
import { Button } from '@/components/ui/Button'
import { Slider } from '@/components/ui/Slider'
import { Checkbox } from '@/components/ui/Checkbox'
import { Badge } from '@/components/ui/Badge'
import { cn } from '@/utils/cn'

interface AccessibilitySettings {
  highContrast: boolean
  largeText: boolean
  reducedMotion: boolean
  screenReaderMode: boolean
  keyboardNavigation: boolean
  focusIndicators: boolean
  fontSize: number
  lineHeight: number
  letterSpacing: number
  colorBlindnessFilter: 'none' | 'protanopia' | 'deuteranopia' | 'tritanopia'
}

interface AccessibilityFeaturesProps {
  onClose?: () => void
  className?: string
}

export function AccessibilityFeatures({ onClose, className }: AccessibilityFeaturesProps) {
  const [settings, setSettings] = useState<AccessibilitySettings>({
    highContrast: false,
    largeText: false,
    reducedMotion: false,
    screenReaderMode: false,
    keyboardNavigation: true,
    focusIndicators: true,
    fontSize: 16,
    lineHeight: 1.5,
    letterSpacing: 0,
    colorBlindnessFilter: 'none',
  })

  const [isVisible, setIsVisible] = useState(false)

  // Load settings from localStorage
  useEffect(() => {
    const saved = localStorage.getItem('accessibility-settings')
    if (saved) {
      try {
        const parsed = JSON.parse(saved)
        setSettings(prev => ({ ...prev, ...parsed }))
      } catch (error) {
        console.error('Failed to parse accessibility settings:', error)
      }
    }
  }, [])

  // Save settings to localStorage and apply them
  useEffect(() => {
    localStorage.setItem('accessibility-settings', JSON.stringify(settings))
    applySettings(settings)
  }, [settings])

  const applySettings = (settings: AccessibilitySettings) => {
    const root = document.documentElement

    // High contrast
    root.classList.toggle('high-contrast', settings.highContrast)

    // Large text
    root.classList.toggle('large-text', settings.largeText)

    // Reduced motion
    root.classList.toggle('reduced-motion', settings.reducedMotion)

    // Screen reader mode
    root.classList.toggle('screen-reader-mode', settings.screenReaderMode)

    // Keyboard navigation
    root.classList.toggle('keyboard-navigation', settings.keyboardNavigation)

    // Focus indicators
    root.classList.toggle('enhanced-focus', settings.focusIndicators)

    // Font settings
    root.style.setProperty('--accessibility-font-size', `${settings.fontSize}px`)
    root.style.setProperty('--accessibility-line-height', settings.lineHeight.toString())
    root.style.setProperty('--accessibility-letter-spacing', `${settings.letterSpacing}px`)

    // Color blindness filter
    const filterMap = {
      none: 'none',
      protanopia: 'url(#protanopia-filter)',
      deuteranopia: 'url(#deuteranopia-filter)',
      tritanopia: 'url(#tritanopia-filter)',
    }
    root.style.setProperty('--accessibility-color-filter', filterMap[settings.colorBlindnessFilter])
  }

  const updateSetting = <K extends keyof AccessibilitySettings>(
    key: K,
    value: AccessibilitySettings[K]
  ) => {
    setSettings(prev => ({ ...prev, [key]: value }))
  }

  const resetSettings = () => {
    const defaultSettings: AccessibilitySettings = {
      highContrast: false,
      largeText: false,
      reducedMotion: false,
      screenReaderMode: false,
      keyboardNavigation: true,
      focusIndicators: true,
      fontSize: 16,
      lineHeight: 1.5,
      letterSpacing: 0,
      colorBlindnessFilter: 'none',
    }
    setSettings(defaultSettings)
  }

  const getActiveFeatureCount = () => {
    return Object.entries(settings).filter(([key, value]) => {
      if (key === 'fontSize') return value !== 16
      if (key === 'lineHeight') return value !== 1.5
      if (key === 'letterSpacing') return value !== 0
      if (key === 'colorBlindnessFilter') return value !== 'none'
      return value === true
    }).length
  }

  if (!isVisible) {
    return (
      <button
        onClick={() => setIsVisible(true)}
        className="fixed bottom-4 left-4 p-3 bg-blue-600 text-white rounded-full shadow-lg hover:shadow-xl transition-all z-50"
        title="Accessibility options"
        aria-label="Open accessibility options"
      >
        <Eye className="h-5 w-5" />
        {getActiveFeatureCount() > 0 && (
          <Badge
            variant="destructive"
            className="absolute -top-1 -right-1 h-5 w-5 flex items-center justify-center text-xs p-0 min-w-0"
          >
            {getActiveFeatureCount()}
          </Badge>
        )}
      </button>
    )
  }

  return (
    <>
      {/* Backdrop */}
      <div
        className="fixed inset-0 bg-black/50 z-50"
        onClick={() => setIsVisible(false)}
      />
      
      {/* Panel */}
      <div className={cn(
        "fixed top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 z-50",
        "bg-card border rounded-lg shadow-xl max-w-2xl w-full mx-4 max-h-[80vh] overflow-hidden",
        className
      )}>
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-blue-100 dark:bg-blue-900 rounded-lg">
              <Eye className="h-5 w-5 text-blue-600 dark:text-blue-400" />
            </div>
            <div>
              <h2 className="text-xl font-semibold">Accessibility Options</h2>
              <p className="text-sm text-muted-foreground">
                Customize the interface for better accessibility
              </p>
            </div>
          </div>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setIsVisible(false)}
          >
            ×
          </Button>
        </div>

        {/* Content */}
        <div className="p-6 overflow-y-auto max-h-[60vh] space-y-6">
          {/* Visual Adjustments */}
          <div className="space-y-4">
            <h3 className="font-semibold flex items-center gap-2">
              <Contrast className="h-4 w-4" />
              Visual Adjustments
            </h3>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="flex items-center justify-between">
                <div>
                  <label className="text-sm font-medium">High Contrast</label>
                  <p className="text-xs text-muted-foreground">
                    Increase contrast for better visibility
                  </p>
                </div>
                <Checkbox
                  checked={settings.highContrast}
                  onCheckedChange={(checked) => updateSetting('highContrast', checked)}
                />
              </div>

              <div className="flex items-center justify-between">
                <div>
                  <label className="text-sm font-medium">Large Text</label>
                  <p className="text-xs text-muted-foreground">
                    Increase text size throughout the app
                  </p>
                </div>
                <Checkbox
                  checked={settings.largeText}
                  onCheckedChange={(checked) => updateSetting('largeText', checked)}
                />
              </div>
            </div>

            {/* Font Size */}
            <div className="space-y-2">
              <label className="text-sm font-medium">
                Font Size: {settings.fontSize}px
              </label>
              <Slider
                value={[settings.fontSize]}
                onValueChange={([value]) => updateSetting('fontSize', value)}
                min={12}
                max={24}
                step={1}
                className="w-full"
              />
            </div>

            {/* Line Height */}
            <div className="space-y-2">
              <label className="text-sm font-medium">
                Line Height: {settings.lineHeight}
              </label>
              <Slider
                value={[settings.lineHeight]}
                onValueChange={([value]) => updateSetting('lineHeight', value)}
                min={1.2}
                max={2.0}
                step={0.1}
                className="w-full"
              />
            </div>

            {/* Color Blindness Filter */}
            <div className="space-y-2">
              <label className="text-sm font-medium">Color Blindness Filter</label>
              <select
                value={settings.colorBlindnessFilter}
                onChange={(e) => updateSetting('colorBlindnessFilter', e.target.value as any)}
                className="w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-ring"
              >
                <option value="none">None</option>
                <option value="protanopia">Protanopia (Red-blind)</option>
                <option value="deuteranopia">Deuteranopia (Green-blind)</option>
                <option value="tritanopia">Tritanopia (Blue-blind)</option>
              </select>
            </div>
          </div>

          {/* Motion & Animation */}
          <div className="space-y-4">
            <h3 className="font-semibold flex items-center gap-2">
              <Zap className="h-4 w-4" />
              Motion & Animation
            </h3>
            
            <div className="flex items-center justify-between">
              <div>
                <label className="text-sm font-medium">Reduced Motion</label>
                <p className="text-xs text-muted-foreground">
                  Minimize animations and transitions
                </p>
              </div>
              <Checkbox
                checked={settings.reducedMotion}
                onCheckedChange={(checked) => updateSetting('reducedMotion', checked)}
              />
            </div>
          </div>

          {/* Navigation */}
          <div className="space-y-4">
            <h3 className="font-semibold flex items-center gap-2">
              <Keyboard className="h-4 w-4" />
              Navigation
            </h3>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="flex items-center justify-between">
                <div>
                  <label className="text-sm font-medium">Enhanced Focus</label>
                  <p className="text-xs text-muted-foreground">
                    Show clear focus indicators
                  </p>
                </div>
                <Checkbox
                  checked={settings.focusIndicators}
                  onCheckedChange={(checked) => updateSetting('focusIndicators', checked)}
                />
              </div>

              <div className="flex items-center justify-between">
                <div>
                  <label className="text-sm font-medium">Keyboard Navigation</label>
                  <p className="text-xs text-muted-foreground">
                    Enable keyboard shortcuts
                  </p>
                </div>
                <Checkbox
                  checked={settings.keyboardNavigation}
                  onCheckedChange={(checked) => updateSetting('keyboardNavigation', checked)}
                />
              </div>
            </div>
          </div>

          {/* Screen Reader */}
          <div className="space-y-4">
            <h3 className="font-semibold flex items-center gap-2">
              <Volume2 className="h-4 w-4" />
              Screen Reader
            </h3>
            
            <div className="flex items-center justify-between">
              <div>
                <label className="text-sm font-medium">Screen Reader Mode</label>
                <p className="text-xs text-muted-foreground">
                  Optimize interface for screen readers
                </p>
              </div>
              <Checkbox
                checked={settings.screenReaderMode}
                onCheckedChange={(checked) => updateSetting('screenReaderMode', checked)}
              />
            </div>
          </div>
        </div>

        {/* Footer */}
        <div className="flex items-center justify-between p-6 border-t bg-muted/50">
          <div className="text-sm text-muted-foreground">
            {getActiveFeatureCount()} accessibility features active
          </div>
          <div className="flex gap-2">
            <Button variant="outline" size="sm" onClick={resetSettings}>
              Reset to Defaults
            </Button>
            <Button size="sm" onClick={() => setIsVisible(false)}>
              Done
            </Button>
          </div>
        </div>
      </div>

      {/* Color Blindness SVG Filters */}
      <svg className="hidden">
        <defs>
          <filter id="protanopia-filter">
            <feColorMatrix values="0.567,0.433,0,0,0 0.558,0.442,0,0,0 0,0.242,0.758,0,0 0,0,0,1,0"/>
          </filter>
          <filter id="deuteranopia-filter">
            <feColorMatrix values="0.625,0.375,0,0,0 0.7,0.3,0,0,0 0,0.3,0.7,0,0 0,0,0,1,0"/>
          </filter>
          <filter id="tritanopia-filter">
            <feColorMatrix values="0.95,0.05,0,0,0 0,0.433,0.567,0,0 0,0.475,0.525,0,0 0,0,0,1,0"/>
          </filter>
        </defs>
      </svg>
    </>
  )
}

// Hook for accessibility features
export function useAccessibility() {
  const [settings, setSettings] = useState<AccessibilitySettings>({
    highContrast: false,
    largeText: false,
    reducedMotion: false,
    screenReaderMode: false,
    keyboardNavigation: true,
    focusIndicators: true,
    fontSize: 16,
    lineHeight: 1.5,
    letterSpacing: 0,
    colorBlindnessFilter: 'none',
  })

  useEffect(() => {
    const saved = localStorage.getItem('accessibility-settings')
    if (saved) {
      try {
        const parsed = JSON.parse(saved)
        setSettings(prev => ({ ...prev, ...parsed }))
      } catch (error) {
        console.error('Failed to parse accessibility settings:', error)
      }
    }
  }, [])

  const updateSetting = <K extends keyof AccessibilitySettings>(
    key: K,
    value: AccessibilitySettings[K]
  ) => {
    setSettings(prev => {
      const newSettings = { ...prev, [key]: value }
      localStorage.setItem('accessibility-settings', JSON.stringify(newSettings))
      return newSettings
    })
  }

  return {
    settings,
    updateSetting,
    isHighContrast: settings.highContrast,
    isLargeText: settings.largeText,
    isReducedMotion: settings.reducedMotion,
    isScreenReaderMode: settings.screenReaderMode,
    isKeyboardNavigation: settings.keyboardNavigation,
    hasFocusIndicators: settings.focusIndicators,
  }
}
