import React, { useState, useEffect } from 'react'
import { HelpCircle, Book, MessageCircle, Video, Search, Star, ChevronRight, X, Lightbulb } from 'lucide-react'
import { Button } from '@/components/ui/Button'
import { Badge } from '@/components/ui/Badge'
import { cn } from '@/utils/cn'

interface HelpTopic {
  id: string
  title: string
  description: string
  category: 'getting-started' | 'search' | 'favorites' | 'settings' | 'troubleshooting'
  content: React.ReactNode
  videoUrl?: string
  relatedTopics?: string[]
}

interface HelpSystemProps {
  onClose?: () => void
  initialTopic?: string
  className?: string
}

export function HelpSystem({ onClose, initialTopic, className }: HelpSystemProps) {
  const [isVisible, setIsVisible] = useState(false)
  const [currentTopic, setCurrentTopic] = useState<string | null>(initialTopic || null)
  const [searchQuery, setSearchQuery] = useState('')
  const [viewHistory, setViewHistory] = useState<string[]>([])

  const helpTopics: HelpTopic[] = [
    {
      id: 'getting-started',
      title: 'Getting Started',
      description: 'Learn the basics of using the video platform',
      category: 'getting-started',
      content: (
        <div className="space-y-4">
          <h3 className="text-lg font-semibold">Welcome to the Video Platform!</h3>
          <p>This guide will help you get started with discovering and enjoying videos.</p>
          
          <div className="space-y-3">
            <div className="p-3 bg-muted rounded-lg">
              <h4 className="font-medium mb-2">🏠 Home Page</h4>
              <p className="text-sm text-muted-foreground">
                Browse trending videos, featured categories, and personalized recommendations.
              </p>
            </div>
            
            <div className="p-3 bg-muted rounded-lg">
              <h4 className="font-medium mb-2">🔍 Search</h4>
              <p className="text-sm text-muted-foreground">
                Use the search bar to find specific videos, categories, or performers.
              </p>
            </div>
            
            <div className="p-3 bg-muted rounded-lg">
              <h4 className="font-medium mb-2">❤️ Favorites</h4>
              <p className="text-sm text-muted-foreground">
                Save videos you like for easy access later.
              </p>
            </div>
          </div>
        </div>
      ),
      relatedTopics: ['search-basics', 'favorites-guide']
    },
    {
      id: 'search-basics',
      title: 'Search Basics',
      description: 'Learn how to search effectively',
      category: 'search',
      content: (
        <div className="space-y-4">
          <h3 className="text-lg font-semibold">How to Search</h3>
          
          <div className="space-y-3">
            <div>
              <h4 className="font-medium mb-2">Basic Search</h4>
              <p className="text-sm text-muted-foreground mb-2">
                Simply type keywords in the search bar and press Enter.
              </p>
              <div className="p-2 bg-muted rounded font-mono text-sm">
                Example: "action movies"
              </div>
            </div>
            
            <div>
              <h4 className="font-medium mb-2">Using Filters</h4>
              <p className="text-sm text-muted-foreground mb-2">
                Click the "Filters" button to narrow down your search by:
              </p>
              <ul className="text-sm text-muted-foreground space-y-1 ml-4">
                <li>• Categories (Action, Comedy, Drama, etc.)</li>
                <li>• Performers</li>
                <li>• Duration (Short, Medium, Long)</li>
                <li>• Quality (HD, 4K)</li>
                <li>• Rating</li>
              </ul>
            </div>
            
            <div>
              <h4 className="font-medium mb-2">Search Tips</h4>
              <ul className="text-sm text-muted-foreground space-y-1 ml-4">
                <li>• Use specific keywords for better results</li>
                <li>• Try different search terms if you don't find what you're looking for</li>
                <li>• Use filters to narrow down large result sets</li>
                <li>• Save frequent searches for quick access</li>
              </ul>
            </div>
          </div>
        </div>
      ),
      relatedTopics: ['advanced-search', 'saved-searches']
    },
    {
      id: 'advanced-search',
      title: 'Advanced Search',
      description: 'Master advanced search techniques',
      category: 'search',
      content: (
        <div className="space-y-4">
          <h3 className="text-lg font-semibold">Advanced Search Techniques</h3>
          
          <div className="space-y-3">
            <div>
              <h4 className="font-medium mb-2">Quick Filters</h4>
              <p className="text-sm text-muted-foreground mb-2">
                Use quick filter buttons for common searches:
              </p>
              <div className="flex gap-2 mb-2">
                <Badge variant="outline">HD</Badge>
                <Badge variant="outline">4K</Badge>
                <Badge variant="outline">Recent</Badge>
                <Badge variant="outline">Popular</Badge>
              </div>
            </div>
            
            <div>
              <h4 className="font-medium mb-2">Saved Searches</h4>
              <p className="text-sm text-muted-foreground">
                Save your search queries and filters for quick access later. 
                Click "Save Current" when viewing search results.
              </p>
            </div>
            
            <div>
              <h4 className="font-medium mb-2">Search Analytics</h4>
              <p className="text-sm text-muted-foreground">
                View search performance metrics and get suggestions for better results.
              </p>
            </div>
          </div>
        </div>
      ),
      relatedTopics: ['search-basics', 'saved-searches']
    },
    {
      id: 'favorites-guide',
      title: 'Managing Favorites',
      description: 'Learn how to save and organize your favorite videos',
      category: 'favorites',
      content: (
        <div className="space-y-4">
          <h3 className="text-lg font-semibold">Managing Your Favorites</h3>
          
          <div className="space-y-3">
            <div>
              <h4 className="font-medium mb-2">Adding to Favorites</h4>
              <p className="text-sm text-muted-foreground mb-2">
                Click the heart icon (❤️) on any video to add it to your favorites.
              </p>
            </div>
            
            <div>
              <h4 className="font-medium mb-2">Viewing Favorites</h4>
              <p className="text-sm text-muted-foreground mb-2">
                Access your favorites from:
              </p>
              <ul className="text-sm text-muted-foreground space-y-1 ml-4">
                <li>• The favorites page in the main navigation</li>
                <li>• The favorites counter in the header</li>
                <li>• Your user profile</li>
              </ul>
            </div>
            
            <div>
              <h4 className="font-medium mb-2">Organizing Favorites</h4>
              <p className="text-sm text-muted-foreground">
                Your favorites are automatically organized by date added. 
                You can search through them or filter by category.
              </p>
            </div>
          </div>
        </div>
      ),
      relatedTopics: ['getting-started']
    },
    {
      id: 'keyboard-shortcuts',
      title: 'Keyboard Shortcuts',
      description: 'Navigate faster with keyboard shortcuts',
      category: 'settings',
      content: (
        <div className="space-y-4">
          <h3 className="text-lg font-semibold">Keyboard Shortcuts</h3>
          
          <div className="space-y-3">
            <div>
              <h4 className="font-medium mb-2">Navigation</h4>
              <div className="space-y-2">
                <div className="flex justify-between items-center">
                  <span className="text-sm">Go to Home</span>
                  <Badge variant="outline" className="font-mono">G + H</Badge>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm">Go to Search</span>
                  <Badge variant="outline" className="font-mono">G + S</Badge>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm">Go to Favorites</span>
                  <Badge variant="outline" className="font-mono">G + F</Badge>
                </div>
              </div>
            </div>
            
            <div>
              <h4 className="font-medium mb-2">Search</h4>
              <div className="space-y-2">
                <div className="flex justify-between items-center">
                  <span className="text-sm">Focus search bar</span>
                  <Badge variant="outline" className="font-mono">/</Badge>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm">Clear search</span>
                  <Badge variant="outline" className="font-mono">Esc</Badge>
                </div>
              </div>
            </div>
            
            <div>
              <h4 className="font-medium mb-2">General</h4>
              <div className="space-y-2">
                <div className="flex justify-between items-center">
                  <span className="text-sm">Toggle theme</span>
                  <Badge variant="outline" className="font-mono">T</Badge>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm">Show shortcuts</span>
                  <Badge variant="outline" className="font-mono">?</Badge>
                </div>
              </div>
            </div>
          </div>
          
          <p className="text-sm text-muted-foreground">
            Press <Badge variant="outline" className="font-mono">?</Badge> anytime to see all available shortcuts.
          </p>
        </div>
      ),
      relatedTopics: ['getting-started']
    },
    {
      id: 'troubleshooting',
      title: 'Troubleshooting',
      description: 'Common issues and solutions',
      category: 'troubleshooting',
      content: (
        <div className="space-y-4">
          <h3 className="text-lg font-semibold">Common Issues</h3>
          
          <div className="space-y-3">
            <div className="p-3 border rounded-lg">
              <h4 className="font-medium mb-2">Videos won't load</h4>
              <ul className="text-sm text-muted-foreground space-y-1 ml-4">
                <li>• Check your internet connection</li>
                <li>• Try refreshing the page</li>
                <li>• Clear your browser cache</li>
                <li>• Disable browser extensions temporarily</li>
              </ul>
            </div>
            
            <div className="p-3 border rounded-lg">
              <h4 className="font-medium mb-2">Search not working</h4>
              <ul className="text-sm text-muted-foreground space-y-1 ml-4">
                <li>• Try different search terms</li>
                <li>• Remove some filters</li>
                <li>• Check for typos in your search</li>
                <li>• Refresh the page</li>
              </ul>
            </div>
            
            <div className="p-3 border rounded-lg">
              <h4 className="font-medium mb-2">Favorites not saving</h4>
              <ul className="text-sm text-muted-foreground space-y-1 ml-4">
                <li>• Enable cookies in your browser</li>
                <li>• Check if you're in private/incognito mode</li>
                <li>• Clear browser data and try again</li>
              </ul>
            </div>
          </div>
          
          <div className="p-3 bg-blue-50 dark:bg-blue-950 rounded-lg">
            <p className="text-sm">
              <strong>Still having issues?</strong> Try refreshing the page or contact support 
              if the problem persists.
            </p>
          </div>
        </div>
      )
    }
  ]

  const categories = {
    'getting-started': { label: 'Getting Started', icon: Lightbulb, color: 'text-green-500' },
    'search': { label: 'Search', icon: Search, color: 'text-blue-500' },
    'favorites': { label: 'Favorites', icon: Star, color: 'text-yellow-500' },
    'settings': { label: 'Settings', icon: HelpCircle, color: 'text-purple-500' },
    'troubleshooting': { label: 'Troubleshooting', icon: MessageCircle, color: 'text-red-500' },
  }

  const filteredTopics = helpTopics.filter(topic =>
    topic.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
    topic.description.toLowerCase().includes(searchQuery.toLowerCase())
  )

  const currentTopicData = currentTopic ? helpTopics.find(t => t.id === currentTopic) : null

  const handleTopicSelect = (topicId: string) => {
    setCurrentTopic(topicId)
    setViewHistory(prev => [...prev.filter(id => id !== topicId), topicId])
  }

  const goBack = () => {
    const history = viewHistory.slice(0, -1)
    setViewHistory(history)
    setCurrentTopic(history[history.length - 1] || null)
  }

  if (!isVisible) {
    return (
      <button
        onClick={() => setIsVisible(true)}
        className="fixed bottom-16 right-4 p-3 bg-green-600 text-white rounded-full shadow-lg hover:shadow-xl transition-all z-50"
        title="Help & Support"
        aria-label="Open help system"
      >
        <HelpCircle className="h-5 w-5" />
      </button>
    )
  }

  return (
    <>
      {/* Backdrop */}
      <div
        className="fixed inset-0 bg-black/50 z-50"
        onClick={() => setIsVisible(false)}
      />
      
      {/* Panel */}
      <div className={cn(
        "fixed top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 z-50",
        "bg-card border rounded-lg shadow-xl max-w-4xl w-full mx-4 max-h-[80vh] overflow-hidden",
        className
      )}>
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b">
          <div className="flex items-center gap-3">
            {currentTopicData && (
              <Button variant="ghost" size="sm" onClick={goBack}>
                ←
              </Button>
            )}
            <div className="p-2 bg-green-100 dark:bg-green-900 rounded-lg">
              <Book className="h-5 w-5 text-green-600 dark:text-green-400" />
            </div>
            <div>
              <h2 className="text-xl font-semibold">
                {currentTopicData ? currentTopicData.title : 'Help & Support'}
              </h2>
              <p className="text-sm text-muted-foreground">
                {currentTopicData ? currentTopicData.description : 'Find answers and learn how to use the platform'}
              </p>
            </div>
          </div>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setIsVisible(false)}
          >
            <X className="h-4 w-4" />
          </Button>
        </div>

        {/* Content */}
        <div className="flex h-[60vh]">
          {!currentTopicData ? (
            <>
              {/* Sidebar */}
              <div className="w-80 border-r p-4 overflow-y-auto">
                {/* Search */}
                <div className="mb-4">
                  <input
                    type="text"
                    placeholder="Search help topics..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="w-full px-3 py-2 border rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-ring"
                  />
                </div>

                {/* Topics by Category */}
                <div className="space-y-4">
                  {Object.entries(categories).map(([categoryKey, category]) => {
                    const categoryTopics = filteredTopics.filter(t => t.category === categoryKey)
                    if (categoryTopics.length === 0) return null
                    
                    const Icon = category.icon
                    
                    return (
                      <div key={categoryKey}>
                        <h3 className="flex items-center gap-2 font-medium mb-2">
                          <Icon className={cn("h-4 w-4", category.color)} />
                          {category.label}
                        </h3>
                        <div className="space-y-1 ml-6">
                          {categoryTopics.map((topic) => (
                            <button
                              key={topic.id}
                              onClick={() => handleTopicSelect(topic.id)}
                              className="w-full text-left p-2 rounded hover:bg-muted transition-colors"
                            >
                              <div className="flex items-center justify-between">
                                <div>
                                  <div className="text-sm font-medium">{topic.title}</div>
                                  <div className="text-xs text-muted-foreground">{topic.description}</div>
                                </div>
                                <ChevronRight className="h-3 w-3 text-muted-foreground" />
                              </div>
                            </button>
                          ))}
                        </div>
                      </div>
                    )
                  })}
                </div>
              </div>

              {/* Main Content */}
              <div className="flex-1 p-6 overflow-y-auto">
                <div className="text-center py-12">
                  <Book className="h-16 w-16 mx-auto mb-4 text-muted-foreground" />
                  <h3 className="text-lg font-semibold mb-2">Welcome to Help & Support</h3>
                  <p className="text-muted-foreground mb-6">
                    Select a topic from the sidebar to get started, or search for specific help.
                  </p>
                  
                  <div className="grid grid-cols-2 gap-4 max-w-md mx-auto">
                    <Button
                      variant="outline"
                      onClick={() => handleTopicSelect('getting-started')}
                      className="h-auto p-4 flex flex-col gap-2"
                    >
                      <Lightbulb className="h-6 w-6" />
                      <span className="text-sm">Getting Started</span>
                    </Button>
                    <Button
                      variant="outline"
                      onClick={() => handleTopicSelect('search-basics')}
                      className="h-auto p-4 flex flex-col gap-2"
                    >
                      <Search className="h-6 w-6" />
                      <span className="text-sm">Search Help</span>
                    </Button>
                  </div>
                </div>
              </div>
            </>
          ) : (
            /* Topic Content */
            <div className="flex-1 p-6 overflow-y-auto">
              {currentTopicData.content}
              
              {/* Related Topics */}
              {currentTopicData.relatedTopics && currentTopicData.relatedTopics.length > 0 && (
                <div className="mt-8 pt-6 border-t">
                  <h4 className="font-medium mb-3">Related Topics</h4>
                  <div className="flex flex-wrap gap-2">
                    {currentTopicData.relatedTopics.map((relatedId) => {
                      const relatedTopic = helpTopics.find(t => t.id === relatedId)
                      if (!relatedTopic) return null
                      
                      return (
                        <Button
                          key={relatedId}
                          variant="outline"
                          size="sm"
                          onClick={() => handleTopicSelect(relatedId)}
                        >
                          {relatedTopic.title}
                        </Button>
                      )
                    })}
                  </div>
                </div>
              )}
            </div>
          )}
        </div>

        {/* Footer */}
        <div className="flex items-center justify-between p-4 border-t bg-muted/50">
          <div className="text-sm text-muted-foreground">
            Need more help? Contact our support team.
          </div>
          <div className="flex gap-2">
            <Button variant="outline" size="sm">
              Contact Support
            </Button>
            <Button size="sm" onClick={() => setIsVisible(false)}>
              Close
            </Button>
          </div>
        </div>
      </div>
    </>
  )
}

// Hook for contextual help
export function useContextualHelp() {
  const [helpTopic, setHelpTopic] = useState<string | null>(null)

  const showHelp = (topicId: string) => {
    setHelpTopic(topicId)
  }

  const hideHelp = () => {
    setHelpTopic(null)
  }

  return {
    helpTopic,
    showHelp,
    hideHelp,
  }
}
