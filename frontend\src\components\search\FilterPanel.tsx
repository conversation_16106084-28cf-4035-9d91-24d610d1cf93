import React, { useState } from 'react'
import { X, ChevronDown, ChevronUp, Star, Clock, Monitor, Filter } from 'lucide-react'
import { Button } from '@/components/ui/Button'
import { Slider } from '@/components/ui/Slider'
import { Checkbox } from '@/components/ui/Checkbox'
// import { Select } from '@/components/ui/Select'
import { Badge } from '@/components/ui/Badge'
import { Collapsible } from '@/components/ui/Collapsible'
import { useAppStore } from '@/store'
import { cn } from '@/utils/cn'
import type { FilterState, Category, Performer } from '@/types'

interface FilterPanelProps {
  filters: FilterState
  onFiltersChange: (filters: FilterState) => void
  onClose?: () => void
  className?: string
}

export function FilterPanel({ filters, onFiltersChange, onClose, className }: FilterPanelProps) {
  const { categories, featuredPerformers } = useAppStore()
  const [expandedSections, setExpandedSections] = useState({
    categories: true,
    performers: true,
    duration: true,
    quality: true,
    rating: true,
    sorting: true,
  })

  const toggleSection = (section: keyof typeof expandedSections) => {
    setExpandedSections(prev => ({
      ...prev,
      [section]: !prev[section]
    }))
  }

  const updateFilters = (updates: Partial<FilterState>) => {
    onFiltersChange({ ...filters, ...updates })
  }

  const clearAllFilters = () => {
    onFiltersChange({
      categories: [],
      performers: [],
      sortBy: 'date_added',
      sortOrder: 'desc',
    })
  }

  const getActiveFilterCount = () => {
    let count = 0
    if (filters.categories.length > 0) count++
    if (filters.performers.length > 0) count++
    if (filters.minDuration || filters.maxDuration) count++
    if (filters.minRating) count++
    return count
  }

  const handleCategoryToggle = (categorySlug: string) => {
    const newCategories = filters.categories.includes(categorySlug)
      ? filters.categories.filter(c => c !== categorySlug)
      : [...filters.categories, categorySlug]
    updateFilters({ categories: newCategories })
  }

  const handlePerformerToggle = (performerSlug: string) => {
    const newPerformers = filters.performers.includes(performerSlug)
      ? filters.performers.filter(p => p !== performerSlug)
      : [...filters.performers, performerSlug]
    updateFilters({ performers: newPerformers })
  }

  const durationRanges = [
    { label: 'Short (< 10 min)', min: 0, max: 600 },
    { label: 'Medium (10-30 min)', min: 600, max: 1800 },
    { label: 'Long (30-60 min)', min: 1800, max: 3600 },
    { label: 'Very Long (> 60 min)', min: 3600, max: undefined },
  ]

  const qualityOptions = [
    { label: 'Any Quality', value: '' },
    { label: '4K Ultra HD', value: '4k' },
    { label: 'Full HD (1080p)', value: '1080p' },
    { label: 'HD (720p)', value: '720p' },
    { label: 'Standard (480p)', value: '480p' },
  ]

  const sortOptions = [
    { label: 'Newest First', value: 'date_added', order: 'desc' },
    { label: 'Oldest First', value: 'date_added', order: 'asc' },
    { label: 'Highest Rated', value: 'rating', order: 'desc' },
    { label: 'Lowest Rated', value: 'rating', order: 'asc' },
    { label: 'Most Viewed', value: 'view_count', order: 'desc' },
    { label: 'Least Viewed', value: 'view_count', order: 'asc' },
    { label: 'Longest Duration', value: 'duration', order: 'desc' },
    { label: 'Shortest Duration', value: 'duration', order: 'asc' },
    { label: 'Title A-Z', value: 'title', order: 'asc' },
    { label: 'Title Z-A', value: 'title', order: 'desc' },
  ]

  return (
    <div className={cn("bg-card border rounded-lg p-6 space-y-6", className)}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <Filter className="h-5 w-5" />
          <h3 className="font-semibold">Filters</h3>
          {getActiveFilterCount() > 0 && (
            <Badge variant="secondary" className="text-xs">
              {getActiveFilterCount()} active
            </Badge>
          )}
        </div>
        <div className="flex items-center gap-2">
          {getActiveFilterCount() > 0 && (
            <Button variant="ghost" size="sm" onClick={clearAllFilters}>
              Clear All
            </Button>
          )}
          {onClose && (
            <Button variant="ghost" size="sm" onClick={onClose}>
              <X className="h-4 w-4" />
            </Button>
          )}
        </div>
      </div>

      {/* Categories */}
      <Collapsible
        open={expandedSections.categories}
        onOpenChange={() => toggleSection('categories')}
      >
        <div className="flex items-center justify-between cursor-pointer" onClick={() => toggleSection('categories')}>
          <h4 className="font-medium">Categories</h4>
          {expandedSections.categories ? (
            <ChevronUp className="h-4 w-4" />
          ) : (
            <ChevronDown className="h-4 w-4" />
          )}
        </div>
        
        {expandedSections.categories && (
          <div className="space-y-2 mt-3">
            <div className="grid grid-cols-2 gap-2 max-h-48 overflow-y-auto">
              {categories.map((category) => (
                <label
                  key={category.id}
                  className="flex items-center space-x-2 cursor-pointer hover:bg-muted/50 p-2 rounded"
                >
                  <Checkbox
                    checked={filters.categories.includes(category.slug)}
                    onCheckedChange={() => handleCategoryToggle(category.slug)}
                  />
                  <span className="text-sm truncate">
                    {category.display_name || category.name}
                  </span>
                  <span className="text-xs text-muted-foreground ml-auto">
                    {category.video_count}
                  </span>
                </label>
              ))}
            </div>
          </div>
        )}
      </Collapsible>

      {/* Performers */}
      <Collapsible
        open={expandedSections.performers}
        onOpenChange={() => toggleSection('performers')}
      >
        <div className="flex items-center justify-between cursor-pointer" onClick={() => toggleSection('performers')}>
          <h4 className="font-medium">Performers</h4>
          {expandedSections.performers ? (
            <ChevronUp className="h-4 w-4" />
          ) : (
            <ChevronDown className="h-4 w-4" />
          )}
        </div>
        
        {expandedSections.performers && (
          <div className="space-y-2 mt-3">
            <div className="grid grid-cols-1 gap-2 max-h-48 overflow-y-auto">
              {featuredPerformers.map((performer) => (
                <label
                  key={performer.id}
                  className="flex items-center space-x-2 cursor-pointer hover:bg-muted/50 p-2 rounded"
                >
                  <Checkbox
                    checked={filters.performers.includes(performer.slug)}
                    onCheckedChange={() => handlePerformerToggle(performer.slug)}
                  />
                  <span className="text-sm truncate">
                    {performer.stage_name || performer.name}
                  </span>
                  <span className="text-xs text-muted-foreground ml-auto">
                    {performer.video_count}
                  </span>
                </label>
              ))}
            </div>
          </div>
        )}
      </Collapsible>

      {/* Duration */}
      <Collapsible
        open={expandedSections.duration}
        onOpenChange={() => toggleSection('duration')}
      >
        <div className="flex items-center justify-between cursor-pointer" onClick={() => toggleSection('duration')}>
          <h4 className="font-medium flex items-center gap-2">
            <Clock className="h-4 w-4" />
            Duration
          </h4>
          {expandedSections.duration ? (
            <ChevronUp className="h-4 w-4" />
          ) : (
            <ChevronDown className="h-4 w-4" />
          )}
        </div>
        
        {expandedSections.duration && (
          <div className="space-y-3 mt-3">
            {durationRanges.map((range, index) => (
              <label
                key={index}
                className="flex items-center space-x-2 cursor-pointer hover:bg-muted/50 p-2 rounded"
              >
                <Checkbox
                  checked={filters.minDuration === range.min && filters.maxDuration === range.max}
                  onCheckedChange={(checked) => {
                    if (checked) {
                      updateFilters({ minDuration: range.min, maxDuration: range.max })
                    } else {
                      updateFilters({ minDuration: undefined, maxDuration: undefined })
                    }
                  }}
                />
                <span className="text-sm">{range.label}</span>
              </label>
            ))}
          </div>
        )}
      </Collapsible>

      {/* Rating */}
      <Collapsible
        open={expandedSections.rating}
        onOpenChange={() => toggleSection('rating')}
      >
        <div className="flex items-center justify-between cursor-pointer" onClick={() => toggleSection('rating')}>
          <h4 className="font-medium flex items-center gap-2">
            <Star className="h-4 w-4" />
            Minimum Rating
          </h4>
          {expandedSections.rating ? (
            <ChevronUp className="h-4 w-4" />
          ) : (
            <ChevronDown className="h-4 w-4" />
          )}
        </div>
        
        {expandedSections.rating && (
          <div className="space-y-3 mt-3">
            <div className="px-2">
              <Slider
                value={[filters.minRating || 0]}
                onValueChange={([value]) => updateFilters({ minRating: value > 0 ? value : undefined })}
                max={10}
                min={0}
                step={0.5}
                className="w-full"
              />
              <div className="flex justify-between text-xs text-muted-foreground mt-1">
                <span>0</span>
                <span className="font-medium">
                  {filters.minRating ? `${filters.minRating}+` : 'Any'}
                </span>
                <span>10</span>
              </div>
            </div>
          </div>
        )}
      </Collapsible>

      {/* Sorting */}
      <Collapsible
        open={expandedSections.sorting}
        onOpenChange={() => toggleSection('sorting')}
      >
        <div className="flex items-center justify-between cursor-pointer" onClick={() => toggleSection('sorting')}>
          <h4 className="font-medium">Sort By</h4>
          {expandedSections.sorting ? (
            <ChevronUp className="h-4 w-4" />
          ) : (
            <ChevronDown className="h-4 w-4" />
          )}
        </div>
        
        {expandedSections.sorting && (
          <div className="space-y-2 mt-3">
            <select
              value={`${filters.sortBy}_${filters.sortOrder}`}
              onChange={(e) => {
                const [sortBy, sortOrder] = e.target.value.split('_')
                updateFilters({ sortBy, sortOrder: sortOrder as 'asc' | 'desc' })
              }}
              className="flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-ring disabled:cursor-not-allowed disabled:opacity-50"
            >
              {sortOptions.map((option) => (
                <option key={`${option.value}_${option.order}`} value={`${option.value}_${option.order}`}>
                  {option.label}
                </option>
              ))}
            </select>
          </div>
        )}
      </Collapsible>
    </div>
  )
}
