import axios, { AxiosInstance, AxiosResponse, AxiosError } from 'axios';
import { toast } from 'react-hot-toast';
import type {
  Video,
  Category,
  Performer,
  VideoSearchRequest,
  PaginatedResponse,
  SearchSuggestionsResponse,
  StatsOverview,
  QualityStats,
  RecommendationResponse,
  Favorite,
  UserPreferences,
  ApiResponse,
} from '@/types';

// API Configuration
const API_BASE_URL = import.meta.env.VITE_API_URL || '/api/v1';
const REQUEST_TIMEOUT = 30000; // 30 seconds

// Create axios instance
const apiClient: AxiosInstance = axios.create({
  baseURL: API_BASE_URL,
  timeout: REQUEST_TIMEOUT,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor
apiClient.interceptors.request.use(
  (config) => {
    // Add user session if available
    const userSession = localStorage.getItem('user_session');
    if (userSession) {
      config.headers['X-User-Session'] = userSession;
    }

    // Add request timestamp
    config.headers['X-Request-Time'] = new Date().toISOString();

    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor
apiClient.interceptors.response.use(
  (response: AxiosResponse) => {
    return response;
  },
  (error: AxiosError) => {
    // Handle common errors
    if (error.response?.status === 401) {
      // Handle unauthorized
      localStorage.removeItem('user_session');
      toast.error('Session expired. Please refresh the page.');
    } else if (error.response?.status === 429) {
      // Handle rate limiting
      toast.error('Too many requests. Please wait a moment.');
    } else if (error.response?.status >= 500) {
      // Handle server errors
      toast.error('Server error. Please try again later.');
    } else if (error.code === 'ECONNABORTED') {
      // Handle timeout
      toast.error('Request timeout. Please check your connection.');
    }

    return Promise.reject(error);
  }
);

// API Service Class
class ApiService {
  // Video endpoints
  async getVideos(params: {
    skip?: number;
    limit?: number;
    category?: string;
    performer?: string;
    sort_by?: string;
    sort_order?: string;
  } = {}): Promise<PaginatedResponse<Video>> {
    const response = await apiClient.get('/videos/', { params });
    return response.data;
  }

  async getVideo(id: number): Promise<Video> {
    const response = await apiClient.get(`/videos/${id}`);
    return response.data;
  }

  async getTrendingVideos(limit: number = 10): Promise<PaginatedResponse<Video>> {
    const response = await apiClient.get('/videos/trending', {
      params: { limit },
    });
    return response.data;
  }

  async getRandomVideos(count: number = 5): Promise<PaginatedResponse<Video>> {
    const response = await apiClient.get('/videos/random', {
      params: { count },
    });
    return response.data;
  }

  // Search endpoints
  async searchVideos(searchRequest: VideoSearchRequest): Promise<PaginatedResponse<Video>> {
    const params: any = {
      q: searchRequest.query,
      skip: searchRequest.skip,
      limit: searchRequest.limit,
    };

    // Handle filters
    if (searchRequest.filters) {
      const { categories, performers, sortBy, sortOrder, ...otherFilters } = searchRequest.filters;

      // Convert arrays to comma-separated strings
      if (categories && categories.length > 0) {
        params.categories = categories.join(',');
      }
      if (performers && performers.length > 0) {
        params.performers = performers.join(',');
      }

      // Add sorting
      if (sortBy) params.sort_by = sortBy;
      if (sortOrder) params.sort_order = sortOrder;

      // Add other filters
      Object.assign(params, otherFilters);
    }

    const response = await apiClient.get('/search/', { params });
    return response.data;
  }

  async getSearchSuggestions(query: string): Promise<SearchSuggestionsResponse> {
    const response = await apiClient.get('/search/suggestions', {
      params: { q: query },
    });
    return response.data;
  }

  async getCategories(): Promise<{ categories: Category[] }> {
    const response = await apiClient.get('/search/categories');
    return response.data;
  }

  async getPerformers(params: {
    skip?: number;
    limit?: number;
    featured?: boolean;
  } = {}): Promise<PaginatedResponse<Performer>> {
    const response = await apiClient.get('/search/performers', { params });
    return response.data;
  }

  // Statistics endpoints
  async getStatsOverview(): Promise<StatsOverview> {
    const response = await apiClient.get('/stats/overview');
    return response.data;
  }

  async getQualityStats(): Promise<QualityStats> {
    const response = await apiClient.get('/stats/quality');
    return response.data;
  }

  async getCategoryStats(): Promise<any> {
    const response = await apiClient.get('/stats/categories');
    return response.data;
  }

  // Recommendations endpoints
  async getSimilarVideos(videoId: number, limit: number = 5): Promise<RecommendationResponse> {
    const response = await apiClient.get(`/recommendations/similar/${videoId}`, {
      params: { limit },
    });
    return response.data;
  }

  async getPersonalizedRecommendations(limit: number = 10): Promise<RecommendationResponse> {
    const response = await apiClient.get('/recommendations/personalized', {
      params: { limit },
    });
    return response.data;
  }

  async getDiscoveryVideos(limit: number = 10): Promise<RecommendationResponse> {
    const response = await apiClient.get('/recommendations/discovery', {
      params: { limit },
    });
    return response.data;
  }

  async getCategoryRecommendations(category: string, limit: number = 10): Promise<RecommendationResponse> {
    const response = await apiClient.get(`/recommendations/category/${category}`, {
      params: { limit },
    });
    return response.data;
  }

  // Favorites endpoints
  async getFavorites(params: {
    skip?: number;
    limit?: number;
  } = {}): Promise<PaginatedResponse<Favorite>> {
    const response = await apiClient.get('/favorites/', { params });
    return response.data;
  }

  async addFavorite(videoId: number): Promise<ApiResponse> {
    const response = await apiClient.post('/favorites/', {
      video_id: videoId,
    });
    return response.data;
  }

  async removeFavorite(videoId: number): Promise<ApiResponse> {
    const response = await apiClient.delete(`/favorites/${videoId}`);
    return response.data;
  }

  async isFavorite(videoId: number): Promise<{ is_favorite: boolean }> {
    const response = await apiClient.get(`/favorites/${videoId}/check`);
    return response.data;
  }

  // User preferences endpoints
  async getUserPreferences(): Promise<UserPreferences> {
    const response = await apiClient.get('/favorites/preferences');
    return response.data;
  }

  async updateUserPreferences(preferences: Partial<UserPreferences>): Promise<ApiResponse> {
    const response = await apiClient.put('/favorites/preferences', preferences);
    return response.data;
  }

  // View logging
  async logView(videoId: number): Promise<void> {
    try {
      await apiClient.post('/favorites/view', {
        video_id: videoId,
      });
    } catch (error) {
      // Silently fail for view logging
      console.warn('Failed to log view:', error);
    }
  }

  // Health check
  async healthCheck(): Promise<{ status: string; message: string }> {
    const response = await apiClient.get('/health', {
      baseURL: '/api', // Use base API without v1
    });
    return response.data;
  }
}

// Create and export API service instance
export const apiService = new ApiService();

// Export individual methods for convenience
export const {
  getVideos,
  getVideo,
  getTrendingVideos,
  getRandomVideos,
  searchVideos,
  getSearchSuggestions,
  getCategories,
  getPerformers,
  getStatsOverview,
  getQualityStats,
  getCategoryStats,
  getSimilarVideos,
  getPersonalizedRecommendations,
  getDiscoveryVideos,
  getCategoryRecommendations,
  getFavorites,
  addFavorite,
  removeFavorite,
  isFavorite,
  getUserPreferences,
  updateUserPreferences,
  logView,
  healthCheck,
} = apiService;

// Utility functions
export const formatDuration = (seconds: number): string => {
  const hours = Math.floor(seconds / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  const remainingSeconds = seconds % 60;

  if (hours > 0) {
    return `${hours}:${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`;
  }
  return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
};

export const formatFileSize = (bytes: number): string => {
  const units = ['B', 'KB', 'MB', 'GB', 'TB'];
  let size = bytes;
  let unitIndex = 0;

  while (size >= 1024 && unitIndex < units.length - 1) {
    size /= 1024;
    unitIndex++;
  }

  return `${size.toFixed(1)} ${units[unitIndex]}`;
};

export const formatResolution = (width: number, height: number): string => {
  if (width >= 3840 && height >= 2160) return '4K';
  if (width >= 1920 && height >= 1080) return '1080p';
  if (width >= 1280 && height >= 720) return '720p';
  if (width >= 854 && height >= 480) return '480p';
  return `${width}x${height}`;
};

export default apiService;
