# Admin Panel Documentation

## Overview

The Admin Panel is a comprehensive backend management system that allows moderators and owners to manage video scraping sites, monitor scraping jobs, and administer users. The system provides a secure, role-based interface for managing the video aggregation platform.

## Features

### 🔐 **Authentication & Authorization**

#### Role-Based Access Control
- **Owner** - Full system access, can manage all users and sites
- **Admin** - Can manage sites, scraping, analytics, and users (except owners)
- **Moderator** - Can manage sites and view analytics
- **Viewer** - Read-only access to analytics

#### Security Features
- **JWT-based authentication** with secure token management
- **Password hashing** using SHA-256 with salt
- **Permission-based access** control for all endpoints
- **Session management** with login tracking
- **Secure password policies** (minimum 8 characters)

### 🌐 **Scraping Site Management**

#### Site Configuration
- **Site Details** - Name, URL, description, and metadata
- **Scraping Settings** - Interval, page limits, rate limiting
- **Content Classification** - Type (video/image), rating, language
- **Priority System** - Higher priority sites scraped first
- **Status Tracking** - Active/inactive, success rates, error monitoring

#### Advanced Configuration
- **Custom Headers** - Set specific HTTP headers for requests
- **Authentication Support** - Handle sites requiring login
- **Rate Limiting** - Configurable delays between requests
- **Scraping Rules** - JSON-based configuration for extraction
- **Quality Metrics** - Track average video quality and success rates

#### Site Management Operations
- **Create Sites** - Add new video sources with full configuration
- **Update Sites** - Modify existing site settings and rules
- **Delete Sites** - Remove sites (with safety checks for running jobs)
- **Bulk Operations** - Activate, deactivate, delete, or scrape multiple sites
- **Site Testing** - Test scraping configuration before deployment

### 📊 **Scraping Job Management**

#### Job Monitoring
- **Real-time Status** - Track pending, running, completed, and failed jobs
- **Progress Tracking** - Pages scraped, videos found, added, updated
- **Error Handling** - Detailed error messages and retry counts
- **Performance Metrics** - Duration, success rates, throughput

#### Job Operations
- **Manual Triggers** - Start scraping jobs for specific sites
- **Job Cancellation** - Stop running jobs when needed
- **Bulk Scheduling** - Queue multiple sites for scraping
- **Job History** - Complete audit trail of all scraping activities

#### Job Analytics
- **Success Rates** - Track job completion and failure rates
- **Performance Trends** - Monitor scraping efficiency over time
- **Resource Usage** - Track system resource consumption
- **Quality Metrics** - Analyze content quality and relevance

### 👥 **User Management**

#### Admin User Administration
- **User Creation** - Add new admin users with role assignment
- **Profile Management** - Update user details and permissions
- **Role Management** - Change user roles and access levels
- **Activity Tracking** - Monitor login history and user actions

#### User Security
- **Password Management** - Secure password hashing and updates
- **Account Status** - Enable/disable user accounts
- **Permission Auditing** - Track user permission changes
- **Session Management** - Monitor active user sessions

### 📈 **Dashboard & Analytics**

#### Overview Dashboard
- **Site Statistics** - Total, active, inactive, and failing sites
- **Scraping Metrics** - Job counts, success rates, videos scraped
- **Recent Activity** - Latest jobs and system events
- **Performance Indicators** - Key metrics and trends

#### Detailed Analytics
- **Site Performance** - Individual site success rates and metrics
- **Content Analysis** - Video quality, categories, and trends
- **System Health** - Resource usage and performance monitoring
- **Historical Data** - Long-term trends and patterns

## API Endpoints

### Authentication
```
POST /api/v1/admin/login
- Authenticate admin user
- Returns JWT token and user details
```

### Dashboard
```
GET /api/v1/admin/dashboard
- Get comprehensive dashboard statistics
- Requires: view_analytics permission
```

### Scraping Sites
```
GET /api/v1/admin/sites
- List all scraping sites with pagination and filtering
- Query params: skip, limit, search, status_filter, sort_by, sort_order

POST /api/v1/admin/sites
- Create new scraping site
- Requires: manage_sites permission

GET /api/v1/admin/sites/{site_id}
- Get specific site details

PUT /api/v1/admin/sites/{site_id}
- Update site configuration

DELETE /api/v1/admin/sites/{site_id}
- Delete site (with safety checks)

POST /api/v1/admin/sites/bulk
- Perform bulk operations on multiple sites
- Actions: activate, deactivate, delete, scrape
```

### Scraping Jobs
```
GET /api/v1/admin/jobs
- List scraping jobs with filtering
- Query params: skip, limit, site_id, status_filter, sort_by, sort_order

POST /api/v1/admin/jobs
- Create new scraping job for a site

GET /api/v1/admin/jobs/{job_id}
- Get specific job details

POST /api/v1/admin/jobs/{job_id}/cancel
- Cancel running job
```

### User Management
```
GET /api/v1/admin/users
- List admin users with pagination
- Query params: skip, limit, search, role_filter

POST /api/v1/admin/users
- Create new admin user
- Requires: manage_users permission

GET /api/v1/admin/users/{user_id}
- Get specific user details

PUT /api/v1/admin/users/{user_id}
- Update user profile and permissions

DELETE /api/v1/admin/users/{user_id}
- Delete admin user (with restrictions)
```

## Database Schema

### Scraping Sites Table
```sql
CREATE TABLE scraping_sites (
    id INTEGER PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    base_url VARCHAR(500) NOT NULL UNIQUE,
    description TEXT,
    is_active BOOLEAN DEFAULT 1,
    priority INTEGER DEFAULT 1,
    scraping_interval_hours INTEGER DEFAULT 24,
    max_pages_per_scrape INTEGER DEFAULT 10,
    site_type VARCHAR(50) DEFAULT 'video',
    content_rating VARCHAR(20) DEFAULT 'adult',
    language VARCHAR(10) DEFAULT 'en',
    country VARCHAR(10),
    scraping_config JSON,
    headers JSON,
    rate_limit_delay REAL DEFAULT 1.0,
    requires_auth BOOLEAN DEFAULT 0,
    auth_config JSON,
    last_scraped_at DATETIME,
    last_successful_scrape DATETIME,
    last_error TEXT,
    total_videos_scraped INTEGER DEFAULT 0,
    failed_scrape_count INTEGER DEFAULT 0,
    average_video_quality REAL,
    success_rate REAL DEFAULT 0.0,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    created_by VARCHAR(255)
);
```

### Scrape Jobs Table
```sql
CREATE TABLE scrape_jobs (
    id INTEGER PRIMARY KEY,
    site_id INTEGER NOT NULL,
    status VARCHAR(20) DEFAULT 'pending',
    started_at DATETIME,
    completed_at DATETIME,
    pages_scraped INTEGER DEFAULT 0,
    videos_found INTEGER DEFAULT 0,
    videos_added INTEGER DEFAULT 0,
    videos_updated INTEGER DEFAULT 0,
    videos_skipped INTEGER DEFAULT 0,
    error_message TEXT,
    error_count INTEGER DEFAULT 0,
    scrape_config_snapshot JSON,
    log_data JSON,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
);
```

### Admin Users Table
```sql
CREATE TABLE admin_users (
    id INTEGER PRIMARY KEY,
    username VARCHAR(100) NOT NULL UNIQUE,
    email VARCHAR(255) NOT NULL UNIQUE,
    hashed_password VARCHAR(255) NOT NULL,
    role VARCHAR(20) DEFAULT 'moderator',
    is_active BOOLEAN DEFAULT 1,
    is_superuser BOOLEAN DEFAULT 0,
    full_name VARCHAR(255),
    avatar_url VARCHAR(500),
    last_login_at DATETIME,
    login_count INTEGER DEFAULT 0,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);
```

## Configuration Examples

### Basic Site Configuration
```json
{
    "name": "Example Video Site",
    "base_url": "https://example-videos.com",
    "description": "High-quality video content aggregator",
    "site_type": "video",
    "content_rating": "adult",
    "language": "en",
    "priority": 5,
    "scraping_interval_hours": 24,
    "max_pages_per_scrape": 10,
    "rate_limit_delay": 1.0,
    "is_active": true
}
```

### Advanced Scraping Configuration
```json
{
    "scraping_config": {
        "video_selector": ".video-item",
        "title_selector": ".video-title",
        "thumbnail_selector": ".video-thumb img",
        "duration_selector": ".video-duration",
        "view_count_selector": ".view-count",
        "pagination_selector": ".pagination .next",
        "max_pages": 10,
        "delay_between_requests": 2.0
    },
    "headers": {
        "User-Agent": "Mozilla/5.0 (compatible; VideoBot/1.0)",
        "Accept": "text/html,application/xhtml+xml",
        "Accept-Language": "en-US,en;q=0.9"
    }
}
```

### Authentication Configuration
```json
{
    "requires_auth": true,
    "auth_config": {
        "type": "form_login",
        "login_url": "https://example.com/login",
        "username_field": "email",
        "password_field": "password",
        "credentials": {
            "username": "scraper_account",
            "password": "encrypted_password"
        }
    }
}
```

## Security Considerations

### Access Control
- **Role-based permissions** prevent unauthorized access
- **Owner-only operations** for critical system changes
- **User isolation** prevents users from modifying others' accounts
- **Permission auditing** tracks all administrative actions

### Data Protection
- **Password hashing** with salt for secure storage
- **Sensitive data encryption** for authentication credentials
- **Input validation** prevents injection attacks
- **Rate limiting** prevents abuse of API endpoints

### Operational Security
- **Job isolation** prevents interference between scraping operations
- **Error handling** prevents information disclosure
- **Audit logging** tracks all administrative activities
- **Session management** with secure token handling

## Default Setup

### Initial Admin Account
```
Username: admin
Password: admin123
Role: owner
Email: <EMAIL>
```

**⚠️ Important: Change the default password immediately after first login!**

### Sample Sites
The system includes three sample scraping sites for testing:
1. **Example Video Site 1** - Active, priority 5
2. **Example Video Site 2** - Inactive, priority 3  
3. **High Quality Video Hub** - Active, priority 8, premium settings

## Usage Examples

### Creating a New Site
```bash
curl -X POST "http://localhost:8000/api/v1/admin/sites" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "New Video Site",
    "base_url": "https://newsite.com",
    "description": "New video content source",
    "priority": 7,
    "scraping_interval_hours": 12
  }'
```

### Starting a Scrape Job
```bash
curl -X POST "http://localhost:8000/api/v1/admin/jobs" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "site_id": 1
  }'
```

### Bulk Site Operations
```bash
curl -X POST "http://localhost:8000/api/v1/admin/sites/bulk" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "action": "activate",
    "site_ids": [1, 2, 3]
  }'
```

The Admin Panel provides a comprehensive, secure, and scalable solution for managing video scraping operations with full audit trails, role-based access control, and detailed monitoring capabilities.
