import React from 'react'
import { TrendingUp, Clock, Search, Filter } from 'lucide-react'
import { Badge } from '@/components/ui/Badge'
import { cn } from '@/utils/cn'

interface SearchAnalyticsProps {
  query: string
  totalResults: number
  searchTime?: number
  appliedFilters: number
  suggestions?: string[]
  className?: string
}

export function SearchAnalytics({
  query,
  totalResults,
  searchTime,
  appliedFilters,
  suggestions = [],
  className,
}: SearchAnalyticsProps) {
  const formatSearchTime = (time: number) => {
    if (time < 1000) {
      return `${time}ms`
    }
    return `${(time / 1000).toFixed(2)}s`
  }

  const getResultsQuality = () => {
    if (totalResults === 0) return { label: 'No results', color: 'text-red-500' }
    if (totalResults < 10) return { label: 'Few results', color: 'text-yellow-500' }
    if (totalResults < 100) return { label: 'Good results', color: 'text-green-500' }
    return { label: 'Many results', color: 'text-blue-500' }
  }

  const quality = getResultsQuality()

  return (
    <div className={cn("bg-muted/50 rounded-lg p-4 space-y-3", className)}>
      {/* Search Stats */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4 text-sm text-muted-foreground">
          <div className="flex items-center gap-1">
            <Search className="h-3 w-3" />
            <span>Query: "{query}"</span>
          </div>
          
          <div className="flex items-center gap-1">
            <TrendingUp className="h-3 w-3" />
            <span className={quality.color}>
              {totalResults.toLocaleString()} results
            </span>
            <Badge variant="outline" className="text-xs">
              {quality.label}
            </Badge>
          </div>

          {searchTime && (
            <div className="flex items-center gap-1">
              <Clock className="h-3 w-3" />
              <span>{formatSearchTime(searchTime)}</span>
            </div>
          )}

          {appliedFilters > 0 && (
            <div className="flex items-center gap-1">
              <Filter className="h-3 w-3" />
              <span>{appliedFilters} filters active</span>
            </div>
          )}
        </div>
      </div>

      {/* Search Suggestions */}
      {suggestions.length > 0 && totalResults < 10 && (
        <div className="space-y-2">
          <p className="text-sm text-muted-foreground">
            Try these related searches:
          </p>
          <div className="flex flex-wrap gap-2">
            {suggestions.slice(0, 5).map((suggestion, index) => (
              <button
                key={index}
                className="text-xs bg-background hover:bg-accent px-2 py-1 rounded border transition-colors"
                onClick={() => {
                  // This would trigger a new search
                  window.location.href = `/search?q=${encodeURIComponent(suggestion)}`
                }}
              >
                {suggestion}
              </button>
            ))}
          </div>
        </div>
      )}

      {/* Search Tips */}
      {totalResults === 0 && (
        <div className="space-y-2">
          <p className="text-sm font-medium text-muted-foreground">
            Search Tips:
          </p>
          <ul className="text-xs text-muted-foreground space-y-1 ml-4">
            <li>• Try different keywords or synonyms</li>
            <li>• Remove some filters to broaden your search</li>
            <li>• Check your spelling</li>
            <li>• Use more general terms</li>
          </ul>
        </div>
      )}
    </div>
  )
}
