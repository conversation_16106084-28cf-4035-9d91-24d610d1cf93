import React from 'react'
import { <PERSON>, useNavigate } from 'react-router-dom'
import { Search, Menu, Heart, BarChart3, Moon, Sun, Monitor, Shield } from 'lucide-react'
import { Button } from '@/components/ui/Button'
import { SearchBar } from '@/components/search/SearchBar'
import { ThemeToggle } from '@/components/ui/ThemeToggle'
import { useAppStore, useTheme, useFavorites } from '@/store'
import { cn } from '@/utils/cn'

export function Header() {
  const navigate = useNavigate()
  const { toggleSidebar, searchOpen, toggleSearch } = useAppStore()
  const { favoriteCount } = useFavorites()

  const handleSearch = (query: string) => {
    if (query.trim()) {
      navigate(`/search?q=${encodeURIComponent(query.trim())}`)
    }
  }

  return (
    <header className="sticky top-0 z-50 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
      <div className="container-responsive flex h-16 items-center justify-between">
        {/* Left section */}
        <div className="flex items-center gap-4">
          <Button
            variant="ghost"
            size="sm"
            onClick={toggleSidebar}
            className="lg:hidden"
          >
            <Menu className="h-5 w-5" />
          </Button>
          
          <Link 
            to="/" 
            className="flex items-center gap-2 font-bold text-xl hover:opacity-80 transition-opacity"
          >
            <div className="w-8 h-8 bg-primary rounded-lg flex items-center justify-center">
              <span className="text-primary-foreground font-bold text-sm">VA</span>
            </div>
            <span className="hidden sm:inline">Video Aggregator</span>
          </Link>
        </div>

        {/* Center section - Search */}
        <div className="flex-1 max-w-2xl mx-4">
          <div className={cn(
            "transition-all duration-300",
            searchOpen ? "block" : "hidden md:block"
          )}>
            <SearchBar
              onSearch={handleSearch}
              placeholder="Search videos, categories, performers..."
              className="w-full"
              data-tour="search-bar"
            />
          </div>
        </div>

        {/* Right section */}
        <div className="flex items-center gap-2">
          <Button
            variant="ghost"
            size="sm"
            onClick={toggleSearch}
            className="md:hidden"
          >
            <Search className="h-5 w-5" />
          </Button>

          <Button
            variant="ghost"
            size="sm"
            asChild
          >
            <Link to="/favorites" className="relative" data-tour="favorites">
              <Heart className="h-5 w-5" />
              {favoriteCount > 0 && (
                <span className="absolute -top-1 -right-1 bg-primary text-primary-foreground text-xs rounded-full w-5 h-5 flex items-center justify-center">
                  {favoriteCount > 99 ? '99+' : favoriteCount}
                </span>
              )}
            </Link>
          </Button>

          <Button
            variant="ghost"
            size="sm"
            asChild
          >
            <Link to="/stats">
              <BarChart3 className="h-5 w-5" />
            </Link>
          </Button>

          <Button
            variant="ghost"
            size="sm"
            asChild
          >
            <Link to="/admin">
              <Shield className="h-5 w-5" />
            </Link>
          </Button>

          <div data-tour="theme-toggle">
            <ThemeToggle />
          </div>
        </div>
      </div>

      {/* Mobile search overlay */}
      {searchOpen && (
        <div className="md:hidden border-t bg-background p-4">
          <SearchBar
            onSearch={handleSearch}
            placeholder="Search videos, categories, performers..."
            autoFocus
          />
        </div>
      )}
    </header>
  )
}
