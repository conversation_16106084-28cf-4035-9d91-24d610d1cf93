"""
Database migration to add admin panel tables.
"""

from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker
import os
import logging
from datetime import datetime
import hashlib
import secrets

logger = logging.getLogger(__name__)

# Database URL from environment
DATABASE_URL = os.getenv("DATABASE_URL", "sqlite:///./app.db")

def hash_password(password: str) -> str:
    """Hash a password using SHA-256 with salt."""
    salt = secrets.token_hex(16)
    return hashlib.sha256((password + salt).encode()).hexdigest() + ":" + salt

def run_migration():
    """Run the migration to add admin tables."""
    engine = create_engine(DATABASE_URL)
    
    with engine.connect() as conn:
        try:
            # Create scraping_sites table
            conn.execute(text("""
                CREATE TABLE IF NOT EXISTS scraping_sites (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    name VARCHA<PERSON>(255) NOT NULL,
                    base_url VARCHAR(500) NOT NULL UNIQUE,
                    description TEXT,
                    is_active BOOLEAN DEFAULT 1,
                    priority INTEGER DEFAULT 1,
                    scraping_interval_hours INTEGER DEFAULT 24,
                    max_pages_per_scrape INTEGER DEFAULT 10,
                    site_type VARCHAR(50) DEFAULT 'video',
                    content_rating VARCHAR(20) DEFAULT 'adult',
                    language VARCHAR(10) DEFAULT 'en',
                    country VARCHAR(10),
                    scraping_config JSON,
                    headers JSON,
                    rate_limit_delay REAL DEFAULT 1.0,
                    requires_auth BOOLEAN DEFAULT 0,
                    auth_config JSON,
                    last_scraped_at DATETIME,
                    last_successful_scrape DATETIME,
                    last_error TEXT,
                    total_videos_scraped INTEGER DEFAULT 0,
                    failed_scrape_count INTEGER DEFAULT 0,
                    average_video_quality REAL,
                    success_rate REAL DEFAULT 0.0,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    created_by VARCHAR(255)
                )
            """))
            
            # Create indexes for scraping_sites
            conn.execute(text("CREATE INDEX IF NOT EXISTS idx_scraping_sites_name ON scraping_sites(name)"))
            conn.execute(text("CREATE INDEX IF NOT EXISTS idx_scraping_sites_active ON scraping_sites(is_active)"))
            conn.execute(text("CREATE INDEX IF NOT EXISTS idx_scraping_sites_priority ON scraping_sites(priority)"))
            
            # Create scrape_jobs table
            conn.execute(text("""
                CREATE TABLE IF NOT EXISTS scrape_jobs (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    site_id INTEGER NOT NULL,
                    status VARCHAR(20) DEFAULT 'pending',
                    started_at DATETIME,
                    completed_at DATETIME,
                    pages_scraped INTEGER DEFAULT 0,
                    videos_found INTEGER DEFAULT 0,
                    videos_added INTEGER DEFAULT 0,
                    videos_updated INTEGER DEFAULT 0,
                    videos_skipped INTEGER DEFAULT 0,
                    error_message TEXT,
                    error_count INTEGER DEFAULT 0,
                    scrape_config_snapshot JSON,
                    log_data JSON,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
                )
            """))
            
            # Create indexes for scrape_jobs
            conn.execute(text("CREATE INDEX IF NOT EXISTS idx_scrape_jobs_site_id ON scrape_jobs(site_id)"))
            conn.execute(text("CREATE INDEX IF NOT EXISTS idx_scrape_jobs_status ON scrape_jobs(status)"))
            
            # Create admin_users table
            conn.execute(text("""
                CREATE TABLE IF NOT EXISTS admin_users (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    username VARCHAR(100) NOT NULL UNIQUE,
                    email VARCHAR(255) NOT NULL UNIQUE,
                    hashed_password VARCHAR(255) NOT NULL,
                    role VARCHAR(20) DEFAULT 'moderator',
                    is_active BOOLEAN DEFAULT 1,
                    is_superuser BOOLEAN DEFAULT 0,
                    full_name VARCHAR(255),
                    avatar_url VARCHAR(500),
                    last_login_at DATETIME,
                    login_count INTEGER DEFAULT 0,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
                )
            """))
            
            # Create indexes for admin_users
            conn.execute(text("CREATE INDEX IF NOT EXISTS idx_admin_users_username ON admin_users(username)"))
            conn.execute(text("CREATE INDEX IF NOT EXISTS idx_admin_users_email ON admin_users(email)"))
            conn.execute(text("CREATE INDEX IF NOT EXISTS idx_admin_users_role ON admin_users(role)"))
            conn.execute(text("CREATE INDEX IF NOT EXISTS idx_admin_users_active ON admin_users(is_active)"))
            
            # Create default admin user if none exists
            result = conn.execute(text("SELECT COUNT(*) as count FROM admin_users")).fetchone()
            if result.count == 0:
                default_password = hash_password("admin123")
                conn.execute(text("""
                    INSERT INTO admin_users (
                        username, email, hashed_password, role, is_active, is_superuser, full_name
                    ) VALUES (
                        'admin', '<EMAIL>', :password, 'owner', 1, 1, 'Default Administrator'
                    )
                """), {"password": default_password})
                
                logger.info("Created default admin user: admin / admin123")
            
            # Add some sample scraping sites
            sample_sites = [
                {
                    "name": "Example Video Site 1",
                    "base_url": "https://example-video-site1.com",
                    "description": "Sample video aggregation site for testing",
                    "site_type": "video",
                    "content_rating": "adult",
                    "language": "en",
                    "priority": 5,
                    "created_by": "admin"
                },
                {
                    "name": "Example Video Site 2", 
                    "base_url": "https://example-video-site2.com",
                    "description": "Another sample video site",
                    "site_type": "video",
                    "content_rating": "adult",
                    "language": "en",
                    "priority": 3,
                    "is_active": False,
                    "created_by": "admin"
                },
                {
                    "name": "High Quality Video Hub",
                    "base_url": "https://hq-videos.example.com",
                    "description": "Premium video content aggregator",
                    "site_type": "video",
                    "content_rating": "adult",
                    "language": "en",
                    "priority": 8,
                    "scraping_interval_hours": 12,
                    "max_pages_per_scrape": 20,
                    "rate_limit_delay": 2.0,
                    "created_by": "admin"
                }
            ]
            
            # Check if sample sites already exist
            existing_sites = conn.execute(text("SELECT COUNT(*) as count FROM scraping_sites")).fetchone()
            if existing_sites.count == 0:
                for site in sample_sites:
                    conn.execute(text("""
                        INSERT INTO scraping_sites (
                            name, base_url, description, site_type, content_rating, 
                            language, priority, is_active, scraping_interval_hours,
                            max_pages_per_scrape, rate_limit_delay, created_by
                        ) VALUES (
                            :name, :base_url, :description, :site_type, :content_rating,
                            :language, :priority, :is_active, :scraping_interval_hours,
                            :max_pages_per_scrape, :rate_limit_delay, :created_by
                        )
                    """), {
                        "name": site["name"],
                        "base_url": site["base_url"],
                        "description": site["description"],
                        "site_type": site["site_type"],
                        "content_rating": site["content_rating"],
                        "language": site["language"],
                        "priority": site["priority"],
                        "is_active": site.get("is_active", True),
                        "scraping_interval_hours": site.get("scraping_interval_hours", 24),
                        "max_pages_per_scrape": site.get("max_pages_per_scrape", 10),
                        "rate_limit_delay": site.get("rate_limit_delay", 1.0),
                        "created_by": site["created_by"]
                    })
                
                logger.info(f"Created {len(sample_sites)} sample scraping sites")
            
            conn.commit()
            logger.info("Admin panel migration completed successfully")
            
        except Exception as e:
            conn.rollback()
            logger.error(f"Migration failed: {e}")
            raise

if __name__ == "__main__":
    logging.basicConfig(level=logging.INFO)
    run_migration()
    print("Migration completed!")
    print("\nDefault admin credentials:")
    print("Username: admin")
    print("Password: admin123")
    print("\nPlease change the default password after first login!")
