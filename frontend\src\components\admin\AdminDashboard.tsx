/**
 * Main admin dashboard component
 */

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../ui/Card';
import { Button } from '../ui/Button';
import { Badge } from '../ui/Badge';
import { <PERSON><PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from '../ui/Tabs';
import { 
  Globe, 
  Play, 
  Pause, 
  CheckCircle, 
  XCircle, 
  Clock, 
  Users, 
  Settings,
  LogOut,
  RefreshCw,
  TrendingUp,
  Activity,
  Database,
  AlertTriangle
} from 'lucide-react';
import { useAdminAuth } from '../../contexts/AdminAuthContext';
import { adminApi, DashboardStats } from '../../services/adminApi';
import { AdminSiteManager } from './AdminSiteManager';
import { AdminJobMonitor } from './AdminJobMonitor';
import { AdminUserManager } from './AdminUserManager';

export const AdminDashboard: React.FC = () => {
  const { user, logout, hasPermission } = useAdminAuth();
  const [stats, setStats] = useState<DashboardStats | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState('');

  const loadDashboardStats = async () => {
    try {
      setIsLoading(true);
      setError('');
      const data = await adminApi.getDashboardStats();
      setStats(data);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load dashboard stats');
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    loadDashboardStats();
  }, []);

  const formatNumber = (num: number) => {
    return new Intl.NumberFormat().format(num);
  };

  const formatPercentage = (num: number) => {
    return `${num.toFixed(1)}%`;
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'bg-green-500';
      case 'running': return 'bg-blue-500';
      case 'completed': return 'bg-green-500';
      case 'failed': return 'bg-red-500';
      case 'failing': return 'bg-orange-500';
      default: return 'bg-gray-500';
    }
  };

  if (isLoading && !stats) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <RefreshCw className="h-8 w-8 animate-spin mx-auto mb-4" />
          <p>Loading dashboard...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-background">
      {/* Header */}
      <header className="border-b bg-card">
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl font-bold">Admin Dashboard</h1>
              <p className="text-muted-foreground">
                Welcome back, {user?.full_name || user?.username}
              </p>
            </div>
            <div className="flex items-center gap-4">
              <Badge variant="outline" className="capitalize">
                {user?.role}
              </Badge>
              <Button
                variant="outline"
                size="sm"
                onClick={loadDashboardStats}
                disabled={isLoading}
              >
                <RefreshCw className={`h-4 w-4 mr-2 ${isLoading ? 'animate-spin' : ''}`} />
                Refresh
              </Button>
              <Button variant="outline" size="sm" onClick={logout}>
                <LogOut className="h-4 w-4 mr-2" />
                Logout
              </Button>
            </div>
          </div>
        </div>
      </header>

      <div className="container mx-auto px-4 py-6">
        {error && (
          <div className="mb-6 p-4 bg-destructive/10 border border-destructive/20 rounded-lg">
            <div className="flex items-center gap-2 text-destructive">
              <AlertTriangle className="h-4 w-4" />
              <span>{error}</span>
            </div>
          </div>
        )}

        {stats && (
          <>
            {/* Stats Overview */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Total Sites</CardTitle>
                  <Globe className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{formatNumber(stats.site_stats.total_sites)}</div>
                  <p className="text-xs text-muted-foreground">
                    {stats.site_stats.active_sites} active, {stats.site_stats.inactive_sites} inactive
                  </p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Running Jobs</CardTitle>
                  <Activity className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{formatNumber(stats.scraping_stats.running_jobs)}</div>
                  <p className="text-xs text-muted-foreground">
                    {stats.scraping_stats.total_jobs} total jobs
                  </p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Videos Scraped</CardTitle>
                  <Database className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{formatNumber(stats.scraping_stats.total_videos_scraped)}</div>
                  <p className="text-xs text-muted-foreground">
                    Across all sites
                  </p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Success Rate</CardTitle>
                  <TrendingUp className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{formatPercentage(stats.scraping_stats.average_success_rate)}</div>
                  <p className="text-xs text-muted-foreground">
                    Average across all sites
                  </p>
                </CardContent>
              </Card>
            </div>

            {/* Recent Activity */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
              <Card>
                <CardHeader>
                  <CardTitle>Recent Jobs</CardTitle>
                  <CardDescription>Latest scraping job activity</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    {stats.recent_jobs.slice(0, 5).map((job) => (
                      <div key={job.id} className="flex items-center justify-between p-3 border rounded-lg">
                        <div className="flex items-center gap-3">
                          <div className={`w-2 h-2 rounded-full ${getStatusColor(job.status)}`} />
                          <div>
                            <p className="font-medium">Job #{job.id}</p>
                            <p className="text-sm text-muted-foreground">
                              Site ID: {job.site_id} • {job.videos_found} videos found
                            </p>
                          </div>
                        </div>
                        <Badge variant="outline" className="capitalize">
                          {job.status}
                        </Badge>
                      </div>
                    ))}
                    {stats.recent_jobs.length === 0 && (
                      <p className="text-center text-muted-foreground py-4">No recent jobs</p>
                    )}
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Top Performing Sites</CardTitle>
                  <CardDescription>Sites with highest video counts</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    {stats.top_performing_sites.slice(0, 5).map((site) => (
                      <div key={site.id} className="flex items-center justify-between p-3 border rounded-lg">
                        <div className="flex items-center gap-3">
                          <div className={`w-2 h-2 rounded-full ${getStatusColor(site.status)}`} />
                          <div>
                            <p className="font-medium">{site.name}</p>
                            <p className="text-sm text-muted-foreground">
                              {formatNumber(site.total_videos_scraped)} videos • {formatPercentage(site.success_rate)} success
                            </p>
                          </div>
                        </div>
                        <Badge variant="outline">
                          Priority {site.priority}
                        </Badge>
                      </div>
                    ))}
                    {stats.top_performing_sites.length === 0 && (
                      <p className="text-center text-muted-foreground py-4">No sites available</p>
                    )}
                  </div>
                </CardContent>
              </Card>
            </div>
          </>
        )}

        {/* Management Tabs */}
        <Tabs defaultValue="sites" className="space-y-6">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="sites" className="flex items-center gap-2">
              <Globe className="h-4 w-4" />
              Sites
            </TabsTrigger>
            <TabsTrigger value="jobs" className="flex items-center gap-2">
              <Activity className="h-4 w-4" />
              Jobs
            </TabsTrigger>
            {hasPermission('manage_users') && (
              <TabsTrigger value="users" className="flex items-center gap-2">
                <Users className="h-4 w-4" />
                Users
              </TabsTrigger>
            )}
          </TabsList>

          <TabsContent value="sites">
            <AdminSiteManager onStatsUpdate={loadDashboardStats} />
          </TabsContent>

          <TabsContent value="jobs">
            <AdminJobMonitor onStatsUpdate={loadDashboardStats} />
          </TabsContent>

          {hasPermission('manage_users') && (
            <TabsContent value="users">
              <AdminUserManager />
            </TabsContent>
          )}
        </Tabs>
      </div>
    </div>
  );
};
